('C:\\Users\\<USER>\\Desktop\\批量去指针\\build\\doubao_tool\\PYZ-00.pyz',
 [('PIL', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageQt.py', 'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageTk.py', 'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\_binary.py', 'PYMODULE'),
  ('PIL._deprecate',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\_typing.py', 'PYMODULE'),
  ('PIL._util', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5', 'D:\\aaaaa\\Lib\\site-packages\\PyQt5\\__init__.py', 'PYMODULE'),
  ('__future__', 'D:\\aaaaa\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\aaaaa\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\aaaaa\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\aaaaa\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\aaaaa\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\aaaaa\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\aaaaa\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\aaaaa\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\aaaaa\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\aaaaa\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\aaaaa\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins', 'D:\\aaaaa\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\aaaaa\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\aaaaa\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\aaaaa\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\aaaaa\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\aaaaa\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\aaaaa\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\aaaaa\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\aaaaa\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\aaaaa\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'D:\\aaaaa\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'D:\\aaaaa\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\aaaaa\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'D:\\aaaaa\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\aaaaa\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\aaaaa\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\aaaaa\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\aaaaa\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\aaaaa\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\aaaaa\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'D:\\aaaaa\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\aaaaa\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\aaaaa\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\aaaaa\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\aaaaa\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\aaaaa\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\aaaaa\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\aaaaa\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'D:\\aaaaa\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\aaaaa\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\aaaaa\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\aaaaa\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\aaaaa\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'D:\\aaaaa\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\aaaaa\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\aaaaa\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\aaaaa\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\aaaaa\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\aaaaa\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\aaaaa\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli', 'D:\\aaaaa\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('bz2', 'D:\\aaaaa\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\aaaaa\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\aaaaa\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\aaaaa\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.model', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('chardet',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\aaaaa\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\aaaaa\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\aaaaa\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'D:\\aaaaa\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'D:\\aaaaa\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\aaaaa\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\aaaaa\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\aaaaa\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\aaaaa\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\aaaaa\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\aaaaa\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\aaaaa\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\aaaaa\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\aaaaa\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\aaaaa\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('dataclasses', 'D:\\aaaaa\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\aaaaa\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\aaaaa\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'D:\\aaaaa\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\aaaaa\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\aaaaa\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv', 'D:\\aaaaa\\Lib\\site-packages\\dotenv\\__init__.py', 'PYMODULE'),
  ('dotenv.ipython',
   'D:\\aaaaa\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main', 'D:\\aaaaa\\Lib\\site-packages\\dotenv\\main.py', 'PYMODULE'),
  ('dotenv.parser',
   'D:\\aaaaa\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\aaaaa\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'D:\\aaaaa\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\aaaaa\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\aaaaa\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\aaaaa\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\aaaaa\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\aaaaa\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\aaaaa\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\aaaaa\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\aaaaa\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\aaaaa\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\aaaaa\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\aaaaa\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\aaaaa\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\aaaaa\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\aaaaa\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\aaaaa\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\aaaaa\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\aaaaa\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\aaaaa\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\aaaaa\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\aaaaa\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\aaaaa\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\aaaaa\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\aaaaa\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\aaaaa\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\aaaaa\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\aaaaa\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\aaaaa\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\aaaaa\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\aaaaa\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\aaaaa\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\aaaaa\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\aaaaa\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\aaaaa\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\aaaaa\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\aaaaa\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\aaaaa\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'D:\\aaaaa\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'D:\\aaaaa\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'D:\\aaaaa\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\aaaaa\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\aaaaa\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\aaaaa\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\aaaaa\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\aaaaa\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\aaaaa\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\aaaaa\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\aaaaa\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\aaaaa\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\aaaaa\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\aaaaa\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\aaaaa\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\aaaaa\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\aaaaa\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib_metadata',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'D:\\aaaaa\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\aaaaa\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.context',
   'D:\\aaaaa\\Lib\\site-packages\\jaraco\\context\\__init__.py',
   'PYMODULE'),
  ('jaraco.functools',
   'D:\\aaaaa\\Lib\\site-packages\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('json', 'D:\\aaaaa\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\aaaaa\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\aaaaa\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\aaaaa\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\aaaaa\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers', 'D:\\aaaaa\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('lzma', 'D:\\aaaaa\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\aaaaa\\Lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'D:\\aaaaa\\Lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'D:\\aaaaa\\Lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'D:\\aaaaa\\Lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\aaaaa\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\aaaaa\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\aaaaa\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\aaaaa\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\aaaaa\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\aaaaa\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\aaaaa\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\aaaaa\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\aaaaa\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\aaaaa\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\aaaaa\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\aaaaa\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\aaaaa\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\aaaaa\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\aaaaa\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\aaaaa\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\aaaaa\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\aaaaa\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\aaaaa\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\aaaaa\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\aaaaa\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\aaaaa\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy', 'D:\\aaaaa\\Lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.__config__',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\aaaaa\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\aaaaa\\Lib\\opcode.py', 'PYMODULE'),
  ('packaging',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\aaaaa\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\aaaaa\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\aaaaa\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\aaaaa\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\aaaaa\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\aaaaa\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'D:\\aaaaa\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\aaaaa\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil', 'D:\\aaaaa\\Lib\\site-packages\\psutil\\__init__.py', 'PYMODULE'),
  ('psutil._common',
   'D:\\aaaaa\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'D:\\aaaaa\\Lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\aaaaa\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\aaaaa\\Lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\aaaaa\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\aaaaa\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\aaaaa\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pyperclip',
   'D:\\aaaaa\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue', 'D:\\aaaaa\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\aaaaa\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\aaaaa\\Lib\\random.py', 'PYMODULE'),
  ('readline', 'D:\\aaaaa\\Lib\\site-packages\\readline.py', 'PYMODULE'),
  ('requests',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\aaaaa\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\aaaaa\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\aaaaa\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\aaaaa\\Lib\\selectors.py', 'PYMODULE'),
  ('selenium',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\bidi\\session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\remote\\websocket_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\support\\select.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\support\\ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'D:\\aaaaa\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\aaaaa\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\aaaaa\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\aaaaa\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\aaaaa\\Lib\\site.py', 'PYMODULE'),
  ('smtplib', 'D:\\aaaaa\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\aaaaa\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\aaaaa\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'D:\\aaaaa\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('ssl', 'D:\\aaaaa\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\aaaaa\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\aaaaa\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\aaaaa\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\aaaaa\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\aaaaa\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\aaaaa\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\aaaaa\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\aaaaa\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\aaaaa\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\aaaaa\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\aaaaa\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'D:\\aaaaa\\Lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.messagebox', 'D:\\aaaaa\\Lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.ttk', 'D:\\aaaaa\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\aaaaa\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\aaaaa\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'D:\\aaaaa\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\aaaaa\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\aaaaa\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\aaaaa\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\aaaaa\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\aaaaa\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\aaaaa\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\aaaaa\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'D:\\aaaaa\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\aaaaa\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\aaaaa\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\aaaaa\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\aaaaa\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\aaaaa\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'D:\\aaaaa\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'D:\\aaaaa\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\aaaaa\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\aaaaa\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\aaaaa\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\aaaaa\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\aaaaa\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\aaaaa\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\aaaaa\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\aaaaa\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\aaaaa\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\aaaaa\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\aaaaa\\Lib\\webbrowser.py', 'PYMODULE'),
  ('webdriver_manager',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'D:\\aaaaa\\Lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('websocket',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'D:\\aaaaa\\Lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('wheel', 'D:\\aaaaa\\Lib\\site-packages\\wheel\\__init__.py', 'PYMODULE'),
  ('wheel._setuptools_logging',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('wheel.bdist_wheel',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('wheel.cli',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util', 'D:\\aaaaa\\Lib\\site-packages\\wheel\\util.py', 'PYMODULE'),
  ('wheel.vendored',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32con',
   'D:\\aaaaa\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\aaaaa\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\aaaaa\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'D:\\aaaaa\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\aaaaa\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter', 'D:\\aaaaa\\Lib\\xml\\dom\\NodeFilter.py', 'PYMODULE'),
  ('xml.dom.domreg', 'D:\\aaaaa\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\aaaaa\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat', 'D:\\aaaaa\\Lib\\xml\\dom\\minicompat.py', 'PYMODULE'),
  ('xml.dom.minidom', 'D:\\aaaaa\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\aaaaa\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder', 'D:\\aaaaa\\Lib\\xml\\dom\\xmlbuilder.py', 'PYMODULE'),
  ('xml.etree', 'D:\\aaaaa\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\aaaaa\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\aaaaa\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\aaaaa\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'D:\\aaaaa\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\aaaaa\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\aaaaa\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\aaaaa\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\aaaaa\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\aaaaa\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\aaaaa\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\aaaaa\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc.server', 'D:\\aaaaa\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('yaml', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\__init__.py', 'PYMODULE'),
  ('yaml.composer',
   'D:\\aaaaa\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\aaaaa\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\cyaml.py', 'PYMODULE'),
  ('yaml.dumper', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\dumper.py', 'PYMODULE'),
  ('yaml.emitter',
   'D:\\aaaaa\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\error.py', 'PYMODULE'),
  ('yaml.events', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\events.py', 'PYMODULE'),
  ('yaml.loader', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\loader.py', 'PYMODULE'),
  ('yaml.nodes', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\nodes.py', 'PYMODULE'),
  ('yaml.parser', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\parser.py', 'PYMODULE'),
  ('yaml.reader', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\reader.py', 'PYMODULE'),
  ('yaml.representer',
   'D:\\aaaaa\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\aaaaa\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\aaaaa\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\aaaaa\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens', 'D:\\aaaaa\\Lib\\site-packages\\yaml\\tokens.py', 'PYMODULE'),
  ('zipfile', 'D:\\aaaaa\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\aaaaa\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob', 'D:\\aaaaa\\Lib\\zipfile\\_path\\glob.py', 'PYMODULE'),
  ('zipimport', 'D:\\aaaaa\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipp', 'D:\\aaaaa\\Lib\\site-packages\\zipp\\__init__.py', 'PYMODULE'),
  ('zipp._functools',
   'D:\\aaaaa\\Lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'D:\\aaaaa\\Lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'D:\\aaaaa\\Lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'D:\\aaaaa\\Lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob', 'D:\\aaaaa\\Lib\\site-packages\\zipp\\glob.py', 'PYMODULE')])
