#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
打包脚本 - 将去指针工具打包成可执行文件
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("✗ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller安装失败: {e}")
        return False

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['doubao_image_processor.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('license.dat', '.'),
    ],
    hiddenimports=[
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.chrome',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support.expected_conditions',
        'webdriver_manager',
        'webdriver_manager.chrome',
        'PyQt5',
        'PyQt5.QtWidgets',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PIL',
        'PIL.Image',
        'numpy',
        'requests',
        'psutil',
        'pyperclip',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='去指针工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('doubao_tool.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ 创建规格文件: doubao_tool.spec")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    try:
        # 使用规格文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "doubao_tool.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 构建成功")
            return True
        else:
            print(f"✗ 构建失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 构建过程出错: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到dist目录"""
    dist_dir = "dist"
    if not os.path.exists(dist_dir):
        print("✗ dist目录不存在")
        return False
    
    files_to_copy = [
        "记忆功能说明.md",
        "license.dat"
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir)
            print(f"✓ 复制文件: {file_name}")
        else:
            print(f"⚠ 文件不存在，跳过: {file_name}")
    
    return True

def create_readme():
    """创建使用说明文件"""
    readme_content = """# 去指针工具 - 使用说明

## 程序简介
这是一个基于豆包AI的图片处理工具，可以自动去除图片中的指针或遮挡物，并可选择去除背景。

## 主要功能
1. **去指针**: 自动识别并去除图片中的指针
2. **去遮挡物**: 自动识别并去除图片中的遮挡物
3. **去背景**: 可选的后处理功能，去除图片背景
4. **记忆功能**: 自动记住用户的选择偏好

## 使用方法
1. 双击运行"去指针工具.exe"
2. 选择输入文件夹（包含要处理的图片）
3. 选择输出文件夹（处理后的图片保存位置）
4. 选择处理类型（去指针或去遮挡物）
5. 可选择是否启用去背景功能
6. 点击"登录"按钮登录豆包AI账号
7. 点击"开始处理"开始批量处理

## 系统要求
- Windows 10/11
- 需要联网使用
- 需要豆包AI账号

## 注意事项
- 首次使用需要登录豆包AI账号
- 程序会自动下载Chrome驱动
- 处理大量图片时请保持网络稳定
- 用户的选择偏好会自动保存

## 技术支持
如有问题请查看日志文件或联系技术支持。

版本: 1.0
更新日期: 2024年
"""
    
    with open("dist/使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✓ 创建使用说明文件")

def main():
    """主函数"""
    print("=" * 50)
    print("去指针工具 - 打包脚本")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，打包失败")
            return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        print("构建失败")
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    # 创建使用说明
    create_readme()
    
    print("\n" + "=" * 50)
    print("✓ 打包完成!")
    print("可执行文件位置: dist/去指针工具.exe")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n打包失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"\n打包过程出现异常: {e}")
        input("按回车键退出...")
