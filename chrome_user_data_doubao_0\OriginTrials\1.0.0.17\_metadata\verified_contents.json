[{"description": "treehash per file", "signed_content": {"payload": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "FmyKp6BeTnEz4O2ZeErhtHi561C6YqZWvYffP8tIXaVpUdqn2H7wE99Czl03-8QinTEJ_PTuudrhNTrUM4e-SOewVi5E4wDijHU1eMgE9A_A2nGBu6vfvKrNIYTp4Ut175fTe4AhWMpbYyrsECEuQNf5AxYpnXg8F3WOqJj5TPWtuPMn2xmiJUkEnRs9okD6guLeMx4yhkdXOme2LnLFAfe6Ulfxew_XHXvZ1Y7MohLS_R1QPl1EIlf2HuJTZllyvNPehR4nJGG8FC--7fI9xw6EAsozvwpTUTKEktRcI1FXWRWlIAmtuK-g3HH_d30putZeNp1bDNUctkDolQKBVQ"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "D-sVn2blf9c5r4WX327IV9uEthjKF5c7FIaguDkWwiu9JDVdCqo1sm_luP-2QUBSDA3q4xUyLea2PtAZ9KP3esURRo-wavtMjl8uX7ZuN-0N7AIAuGXNdlJjNzRzh1qceLNzjrbBlzZ6SfUnlp-weYO_7WejwtEqWlhW5ONlkl0ERjx2JPm40vRYNVTTT8DogbVrCblqoDKecM69VjT45t24FOIotccbbDMQ69e4sR0EXE5JkPhMtRFLolCNEiIlvXGTVh2YYIp1zPwn2ltyY8wiDo_Nr9rUk5_7mStutc-zjvLIL6q1ORy70xBglfMAa6GddnLSknqcQYIeH5uW1A"}]}}]