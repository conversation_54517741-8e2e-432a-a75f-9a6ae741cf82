[3624:50096:0725/223522.766:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[38196:50268:0725/223522.911:ERROR:gpu\ipc\service\gpu_channel_manager.cc:946] Failed to create GLES3 context, fallback to GLES2.
[38196:50268:0725/223522.920:ERROR:gpu\ipc\service\gpu_channel_manager.cc:957] ContextResult::kFatalFailure: Failed to create shared context for virtualization.
[38196:50268:0725/223523.203:ERROR:gpu\ipc\service\gpu_channel_manager.cc:946] Failed to create GLES3 context, fallback to GLES2.
[38196:50268:0725/223523.203:ERROR:gpu\ipc\service\gpu_channel_manager.cc:957] ContextResult::kFatalFailure: Failed to create shared context for virtualization.
[38196:50268:0725/223523.204:ERROR:gpu\ipc\service\gpu_channel_manager.cc:946] Failed to create GLES3 context, fallback to GLES2.
[38196:50268:0725/223523.205:ERROR:gpu\ipc\service\gpu_channel_manager.cc:957] ContextResult::kFatalFailure: Failed to create shared context for virtualization.
[3624:50096:0725/223523.750:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[3624:50096:0725/223526.801:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223526.816:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[80648:9532:0725/223526.873:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[3624:50096:0725/223526.954:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[76900:35748:0725/223527.023:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223527.027:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223527.027:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4cac300:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[3624:50096:0725/223527.975:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223528.372:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[3624:50096:0725/223529.957:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223529.957:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223529.957:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223530.346:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223530.351:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[76900:35748:0725/223530.991:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[80648:9532:0725/223531.415:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[3624:50096:0725/223531.429:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[76900:35748:0725/223531.440:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223531.441:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223531.441:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4cae100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[3624:71184:0725/223532.819:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[76900:35748:0725/223534.527:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223534.528:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223534.528:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[13df00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[3624:50096:0725/223536.409:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223536.736:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[76900:35748:0725/223537.889:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[3624:50096:0725/223538.543:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223538.544:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223538.544:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[76900:35748:0725/223538.856:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223538.856:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223538.858:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2350f00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[3624:50096:0725/223539.125:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223539.131:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[80648:9532:0725/223539.800:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[3624:50096:0725/223539.815:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[76900:35748:0725/223539.833:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223539.836:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223539.837:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[13df00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[76900:35748:0725/223542.892:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[3624:50096:0725/223545.190:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223545.593:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[3624:50096:0725/223547.271:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223547.271:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223547.271:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223547.703:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223547.737:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[76900:35748:0725/223547.765:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223547.765:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223547.766:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2350f00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[76900:35748:0725/223547.899:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[80648:9532:0725/223548.837:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[3624:50096:0725/223548.863:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[76900:35748:0725/223548.997:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223548.998:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223549.001:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[13df00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[76900:35748:0725/223552.916:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[3624:50096:0725/223553.725:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223554.000:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[3624:50096:0725/223556.149:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223556.149:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223556.149:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[3624:50096:0725/223557.070:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[3624:50096:0725/223557.102:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[80648:9532:0725/223557.122:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[3624:50096:0725/223557.135:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[76900:35748:0725/223557.150:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223557.151:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223557.152:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2350f00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[76900:35748:0725/223557.209:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223557.210:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223557.229:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4c9a500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[76900:35748:0725/223557.918:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[3624:71184:0725/223559.843:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[76900:35748:0725/223602.923:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[76900:35748:0725/223604.122:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[76900:35748:0725/223604.122:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[80648:16844:0725/223604.122:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4ca6900:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[76900:35748:0725/223607.955:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[76900:35748:0725/223612.956:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[76900:35748:0725/223617.957:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[76900:35748:0725/223622.959:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[80648:16844:0725/223626.167:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[76900:35748:0725/223627.972:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[76900:35748:0725/223632.974:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[80648:16844:0725/223633.063:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[76900:35748:0725/223640.592:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[80648:16844:0725/223642.590:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
