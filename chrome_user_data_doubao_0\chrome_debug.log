[52792:72172:0725/162826.270:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[76584:74908:0725/162826.415:ERROR:gpu\ipc\service\gpu_channel_manager.cc:946] Failed to create GLES3 context, fallback to GLES2.
[76584:74908:0725/162826.419:ERROR:gpu\ipc\service\gpu_channel_manager.cc:957] ContextResult::kFatalFailure: Failed to create shared context for virtualization.
[76584:74908:0725/162826.564:ERROR:gpu\ipc\service\gpu_channel_manager.cc:946] Failed to create GLES3 context, fallback to GLES2.
[76584:74908:0725/162826.565:ERROR:gpu\ipc\service\gpu_channel_manager.cc:957] ContextResult::kFatalFailure: Failed to create shared context for virtualization.
[76584:74908:0725/162826.566:ERROR:gpu\ipc\service\gpu_channel_manager.cc:946] Failed to create GLES3 context, fallback to GLES2.
[76584:74908:0725/162826.566:ERROR:gpu\ipc\service\gpu_channel_manager.cc:957] ContextResult::kFatalFailure: Failed to create shared context for virtualization.
[52792:72172:0725/162827.140:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/162829.921:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162829.937:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162830.193:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162830.483:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[46232:53852:0725/162830.737:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/162830.792:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162831.023:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162831.029:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162831.048:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[226a500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[52792:72172:0725/162832.285:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162832.285:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162832.285:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162832.998:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162833.015:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/162833.535:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/162833.565:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162833.597:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162833.597:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162833.599:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2268200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162834.348:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:34432:0725/162836.483:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[26708:26792:0725/162837.535:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162837.535:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162837.535:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[226a500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[52792:72172:0725/162838.685:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162839.042:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/162840.806:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162840.806:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162840.806:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162841.363:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162841.367:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162841.659:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162841.659:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162841.659:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2268200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162841.863:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:53852:0725/162842.118:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/162842.137:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162842.159:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162842.159:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162842.160:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[226a500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162846.865:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/162847.310:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162847.571:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/162849.197:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162849.198:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162849.198:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162849.456:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162849.460:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162849.565:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162849.566:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162849.566:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2268200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[46232:53852:0725/162850.694:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/162850.711:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162850.859:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162850.859:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162850.860:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[226a500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162851.870:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/162855.680:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162855.920:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/162856.871:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/162857.407:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162857.407:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162857.407:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162858.010:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162858.025:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162858.635:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162858.635:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162858.635:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2268200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[46232:53852:0725/162858.974:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/162858.987:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162859.003:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162859.003:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162859.004:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[226a500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162901.879:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:34432:0725/162905.794:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[26708:26792:0725/162905.973:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162905.973:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162905.974:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2268200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162906.884:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/162911.888:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/162916.175:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162916.762:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/162916.890:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/162918.259:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162918.259:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162918.259:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162918.612:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162918.636:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/162919.810:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/162919.826:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162919.840:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162919.840:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162919.840:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1a63200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162921.893:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/162926.895:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/162926.921:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162926.921:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162926.921:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[a134100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162931.897:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/162933.876:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/162936.210:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162936.474:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/162936.905:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/162937.936:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162937.936:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162937.936:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162938.481:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162938.490:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/162939.565:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/162939.581:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162939.595:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162939.595:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162939.596:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4eebd00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/162941.923:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/162946.924:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/162947.664:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162947.664:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162947.664:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[82c8000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[52792:72172:0725/162951.322:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162951.653:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/162951.927:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/162953.016:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162953.016:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162953.016:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/162953.641:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/162953.649:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:34432:0725/162953.751:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[46232:53852:0725/162954.719:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/162954.736:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/162954.749:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/162954.750:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/162954.751:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3adcd00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[46232:63604:0725/162956.720:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/162956.928:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163001.931:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163002.632:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163002.633:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163002.633:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3b05500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163004.949:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163006.339:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163006.657:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163006.932:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163008.195:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163008.195:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163008.195:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163008.546:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163008.555:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:63604:0725/163008.698:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[46232:53852:0725/163009.717:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163009.739:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163009.754:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163009.755:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163009.755:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ac3200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163009.949:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163010.822:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -100
[26708:26792:0725/163011.934:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163014.951:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163016.723:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163016.723:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163016.723:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[185d200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163016.935:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163019.953:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163021.938:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163024.958:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163025.786:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163026.557:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163026.893:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163026.939:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163028.389:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163028.389:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163028.389:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163028.714:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163028.723:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163029.938:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163029.951:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163029.962:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163029.967:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163029.967:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163029.967:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[a135a00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163031.941:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163034.965:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163036.941:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163037.638:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163037.638:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163037.638:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[155e00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163039.966:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163041.545:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163041.957:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163042.975:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163044.547:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163044.547:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163044.547:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163044.860:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163044.875:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163046.047:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163046.068:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163046.087:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163046.087:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163046.087:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3b0dc00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163046.959:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163047.518:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163049.945:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163051.962:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163052.519:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163053.063:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163053.063:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163053.063:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ad8700:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163056.969:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163059.616:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163101.089:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163101.674:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163101.990:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163103.684:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163103.685:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163103.685:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163104.132:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163104.155:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163104.622:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:53852:0725/163105.043:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163105.057:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163105.070:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163105.071:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163105.071:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2267800:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163108.543:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163111.642:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163112.641:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163112.641:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163112.642:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4eec200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163113.545:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163116.643:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:34432:0725/163117.395:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[52792:72172:0725/163117.961:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163118.237:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[46232:63604:0725/163118.631:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163119.826:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163119.826:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163119.826:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163120.056:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163120.062:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163121.294:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163121.309:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163121.321:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163121.322:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163121.322:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[a13a000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163124.815:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163128.297:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163128.297:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163128.297:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2254100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163129.817:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163136.612:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163137.515:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163138.142:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163138.530:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163140.124:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163140.124:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163140.124:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163140.467:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163140.479:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163141.579:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163141.595:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163141.613:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163141.613:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163141.613:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ac5f00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163142.517:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163147.520:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163149.667:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163149.667:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163149.667:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2254100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163152.522:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163153.723:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163153.998:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163155.612:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163155.612:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163155.612:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163155.865:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163155.871:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163157.055:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163157.082:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163157.100:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163157.101:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163157.101:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[e68b800:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163200.573:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163203.092:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163204.046:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163204.047:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163204.047:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[159500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163205.575:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163210.578:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163211.517:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163214.195:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163214.448:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163215.579:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163215.982:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163215.982:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163215.982:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163216.450:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163216.473:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163217.480:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163217.500:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163217.527:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163217.528:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163217.528:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[155400:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163220.975:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163221.513:ERROR:third_party\webrtc\p2p\base\stun_port.cc:117] Binding request timed out from 0.0.0.x:63528 (any)
[26708:26792:0725/163224.562:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163224.562:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163224.563:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2268200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163225.979:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163229.229:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163229.476:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163230.913:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163230.913:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163230.913:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[26708:26792:0725/163230.981:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163231.366:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163231.374:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163232.543:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163232.558:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163232.574:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163232.574:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163232.575:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[82dfd00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[46232:63604:0725/163233.533:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163235.983:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163239.568:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163239.568:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163239.569:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2254100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163240.984:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163245.985:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163249.386:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163249.641:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[46232:63604:0725/163250.028:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163250.988:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163251.062:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163251.062:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163251.062:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163251.559:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163251.584:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163252.691:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163252.705:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163252.724:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163252.724:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163252.725:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ac5500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163256.003:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163300.650:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163300.653:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163300.653:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1850f00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163301.005:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163304.437:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163304.719:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163306.007:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163306.296:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163306.296:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163306.296:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163306.732:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163306.742:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163307.763:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163307.778:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:63604:0725/163308.373:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163308.873:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163308.874:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163308.875:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1a67300:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163311.008:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163314.759:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163314.759:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163314.759:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[a13af00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163316.009:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163321.010:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163324.597:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163324.940:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[46232:63604:0725/163325.321:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163326.013:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163326.548:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163326.548:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163326.548:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163326.934:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163326.960:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163327.991:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163328.003:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163328.016:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163328.016:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163328.016:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[82b9900:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163331.019:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163335.647:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163335.647:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163335.647:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[9d78f00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163336.023:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163339.501:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163339.854:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163341.042:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163341.374:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163341.374:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163341.374:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163341.933:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163341.941:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163342.968:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163342.993:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163343.017:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163343.017:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163343.019:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[161c00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[46232:63604:0725/163344.720:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163346.046:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163349.958:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163349.959:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163349.960:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2260000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163351.048:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163356.048:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163359.592:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163359.669:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163359.955:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163401.057:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163401.502:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163401.502:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163401.502:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163401.734:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163401.752:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163403.021:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163403.041:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163403.055:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163403.055:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163403.056:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[a199b00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163406.059:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163410.653:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163410.653:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163410.653:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4ee7200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163411.066:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163414.620:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163414.865:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163416.070:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163416.351:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163416.352:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163416.352:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163416.818:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163416.824:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163417.954:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163417.966:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163417.981:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163417.982:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163417.982:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[82bcb00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163421.074:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163423.048:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163424.959:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163424.960:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163424.965:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ad0000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163426.078:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163431.082:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163433.032:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163434.957:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163435.238:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163436.086:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163436.786:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163436.786:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163436.786:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163437.179:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163437.188:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163438.357:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163438.371:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163438.392:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163438.392:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163438.392:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ad9100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163441.090:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163445.535:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163445.535:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163445.535:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ad2800:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163446.093:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:34432:0725/163449.702:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[52792:72172:0725/163449.896:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163450.248:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163451.095:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163451.819:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163451.819:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163451.819:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163452.065:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163452.095:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163453.311:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163453.330:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163453.349:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163453.350:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163453.350:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[185e100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163456.095:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163458.764:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163500.302:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163500.303:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163500.303:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[a195500:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163501.099:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163509.635:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163509.935:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163510.204:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[46232:63604:0725/163510.779:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163511.651:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163511.651:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163511.651:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163512.078:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163512.089:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163513.255:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163513.273:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163513.293:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163513.293:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163513.294:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4ef2600:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163514.637:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163520.561:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163520.567:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163520.593:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3b0a000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163522.938:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163523.612:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163525.107:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163525.516:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163527.088:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163527.088:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163527.088:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163527.593:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163527.602:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163527.942:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:53852:0725/163528.566:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163528.580:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163528.613:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163528.614:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163528.615:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163528.620:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[afb8b00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[46232:63604:0725/163529.942:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163532.948:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163533.620:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163535.569:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163535.569:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163535.569:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4ee4000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163537.965:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163538.621:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163542.966:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163543.631:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163545.139:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163545.422:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[46232:63604:0725/163545.836:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163546.943:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163546.943:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163546.943:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163547.437:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163547.471:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163547.973:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:53852:0725/163548.494:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163548.519:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163548.538:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163548.539:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163548.539:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[4ef0d00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163548.633:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163552.978:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163553.636:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163555.549:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163555.549:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163555.549:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ade100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163557.979:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163558.644:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163600.438:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163600.695:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163602.126:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163602.126:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163602.126:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163602.636:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163602.646:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163602.985:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:53852:0725/163603.741:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163603.760:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163603.796:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163603.802:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163603.814:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[f2d7300:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163606.528:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163607.987:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163609.330:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163610.747:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163610.747:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163610.748:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3ad6900:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163611.532:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163612.990:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163617.992:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163618.561:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163619.374:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163620.620:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163620.925:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163622.424:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163622.424:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163622.424:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163622.907:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163622.920:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163623.562:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:53852:0725/163623.985:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163624.005:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163624.019:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163624.019:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163624.020:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1a6eb00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163627.346:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163630.565:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163631.637:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163631.637:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163631.637:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2258200:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163632.347:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163635.567:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163635.784:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163636.048:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163637.657:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163637.657:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163637.657:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163637.988:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163637.995:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163639.142:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163639.158:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163639.174:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163639.174:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163639.174:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1a6dc00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163642.710:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163644.286:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163646.141:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163646.141:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163646.141:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1a6e600:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163647.711:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163655.526:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163656.214:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163656.450:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[46232:63604:0725/163656.846:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163657.897:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163657.897:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163657.897:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163658.461:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163658.484:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163659.502:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163659.531:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163659.550:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163659.550:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163659.550:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1859600:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163700.527:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163705.532:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163706.546:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163706.546:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163706.546:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[226a000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163710.533:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163711.191:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163711.450:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163712.877:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163712.877:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163712.877:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163713.425:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163713.434:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163714.510:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163714.536:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163714.556:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163714.556:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163714.557:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3acb900:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163717.886:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163719.627:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163721.485:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163721.485:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163721.485:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1a62d00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163722.888:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163727.891:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163729.566:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163731.471:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163731.831:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163732.905:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163733.414:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163733.414:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163733.415:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163733.867:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163733.890:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163734.881:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163734.895:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163734.916:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163734.916:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163734.917:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2266e00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163737.908:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163742.652:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163742.652:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163742.653:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[82b8a00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163742.913:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163746.451:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163746.694:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[26708:26792:0725/163747.921:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163748.084:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163748.084:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163748.084:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163748.615:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163748.622:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163749.754:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163749.769:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163749.782:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163749.783:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163749.783:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3b00000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[46232:63604:0725/163750.121:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163752.929:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163756.747:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163756.747:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163756.747:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[3b04b00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163757.939:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163802.940:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163806.125:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163806.516:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[46232:63604:0725/163806.899:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163807.942:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163808.037:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163808.037:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163808.037:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163808.339:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163808.347:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163809.581:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163809.597:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163809.612:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163809.612:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163809.713:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[82bc100:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163813.119:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163817.096:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163817.096:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163817.235:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2251400:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163818.612:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163821.741:WARNING:chrome\browser\performance_manager\policies\page_discarding_helper.cc:137] Discarding multiple pages with target (kb): 0, discard_protected_tabs: 0
[26708:26792:0725/163823.733:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163828.746:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[52792:72172:0725/163830.321:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163830.607:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163832.097:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163832.098:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163832.098:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163832.403:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163832.411:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[46232:53852:0725/163833.662:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163833.688:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163833.762:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163833.790:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163833.790:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163833.810:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[9d3a300:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[46232:63604:0725/163833.830:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163838.807:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163840.662:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163840.663:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163840.663:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[1859600:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163843.808:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163848.823:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163849.667:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[52792:72172:0725/163850.453:INFO:CONSOLE:2] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163851.755:INFO:CONSOLE:0] "The Content Security Policy directive 'upgrade-insecure-requests' is ignored when delivered in a report-only policy.", source: https://www.doubao.com/chat/ (0)
[52792:72172:0725/163853.194:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.google.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163853.195:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://www.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163853.195:INFO:CONSOLE:574] "Failed to execute 'postMessage' on 'DOMWindow': The target origin provided ('https://m.youtube.com') does not match the recipient window's origin ('https://www.doubao.com').", source: https://www.googletagmanager.com/gtag/js?id=G-G8EP5CG8VZ (574)
[52792:72172:0725/163853.672:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[52792:72172:0725/163853.680:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163853.825:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:53852:0725/163854.804:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[52792:72172:0725/163854.825:INFO:CONSOLE:2] "%c [object HTMLImageElement]", source: https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/rc-client-security/c-webmssdk/********/webmssdk.es5.js (2)
[26708:26792:0725/163854.845:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163854.846:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163854.846:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[6467e00:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163858.827:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163902.533:WARNING:net\dns\host_resolver_dns_task.cc:938] Address list empty after RFC3484 sort
[26708:26792:0725/163902.533:ERROR:services\network\p2p\socket_manager.cc:137] Failed to resolve address for stun.l.google.com., errorcode: -105
[46232:63604:0725/163902.534:WARNING:third_party\webrtc\p2p\base\stun_port.cc:455] Port[2267300:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: StunPort: stun host lookup received error -1
[26708:26792:0725/163903.829:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163908.832:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163913.651:ERROR:third_party\webrtc\p2p\base\stun_port.cc:117] Binding request timed out from 0.0.0.x:53333 (any)
[26708:26792:0725/163913.843:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[46232:63604:0725/163914.158:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[46232:63604:0725/163914.158:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[46232:63604:0725/163920.742:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[26708:26792:0725/163921.527:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[26708:26792:0725/163926.531:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
