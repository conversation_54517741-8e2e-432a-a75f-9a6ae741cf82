#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
豆包AI图片处理自动化工具
功能：批量处理图片（去指针或去遮挡物）
作者：AI助手
"""

import sys
import os
import time
import json
import uuid
import hashlib
import datetime
import random
import gc
import tkinter as tk
from tkinter import ttk, messagebox
try:
    import pyperclip
except ImportError:
    pyperclip = None
try:
    import psutil
except ImportError:
    psutil = None
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QFileDialog, QProgressBar, QComboBox,
                            QCheckBox, QMessageBox, QListWidget, QGroupBox, QRadioButton,
                            QButtonGroup, QSpinBox, QLineEdit, QMenu, QAction, QSizePolicy,
                            QGridLayout, QScrollArea, QD<PERSON>og, QFormLayout)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize, QTimer, QMetaObject, Q_ARG, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import urllib.request
import logging
import numpy as np
from PIL import Image
import requests
import threading
import queue
from concurrent.futures import ThreadPoolExecutor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 禁用Selenium和urllib3的日志
logging.getLogger('selenium').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('webdriver_manager').setLevel(logging.ERROR)

# ==================== 激活验证系统 ====================
import base64
import zlib
import struct
import time

# 混淆的常量和函数
def _x1():
    return base64.b64decode(b'VHJhZUFJMjAyNA==').decode()

def _x2():
    return base64.b64decode(b'bGljZW5zZS5kYXQ=').decode()

def _x3():
    import psutil
    try:
        procs = [p.name().lower() for p in psutil.process_iter(['name'])]
        debuggers = ['ollydbg', 'x64dbg', 'ida', 'windbg', 'cheatengine', 'processhacker', 'wireshark']
        return not any(dbg in proc for proc in procs for dbg in debuggers)
    except:
        return True

def _x4():
    current = int(time.time())
    return 1704067200 < current < 1893456000

def _x5():
    try:
        # 简化机器码算法，与你已知的机器码匹配
        mac = uuid.getnode()
        comp = os.environ.get('COMPUTERNAME', 'unknown')
        info = f"{mac}_{comp}"
        return hashlib.md5(info.encode()).hexdigest()
    except:
        return hashlib.md5(str(uuid.getnode()).encode()).hexdigest()

def _x6(mc, days=36500, minutes=0):
    # 支持天数和分钟数
    if minutes > 0:
        ed = (datetime.datetime.now() + datetime.timedelta(minutes=minutes)).strftime("%Y%m%d%H%M")
    else:
        ed = (datetime.datetime.now() + datetime.timedelta(days=days)).strftime("%Y%m%d")

    entropy = "FIXED1"  # 使用固定熵值确保一致性

    s1 = hashlib.sha256((mc + entropy + _x1()).encode()).hexdigest()
    s2 = hashlib.md5((s1 + ed).encode()).hexdigest()
    s3 = hashlib.sha256((s2 + mc[:16] + _x1()[:4]).encode()).hexdigest().upper()

    checksum = sum(ord(c) for c in s3) % 256
    return f"{s3}:{ed}:{entropy}:{checksum:02X}"

def _x7(code):
    try:
        if not code or code.count(':') != 3:
            return False

        parts = code.split(":")
        if len(parts) != 4:
            return False

        ac, ed, entropy, checksum = parts

        # 支持两种时间格式：YYYYMMDD（8位）和YYYYMMDDHHMM（12位）
        if len(ed) not in [8, 12] or len(entropy) != 6 or len(checksum) != 2:
            return False

        # 检查是否过期
        if len(ed) == 12:
            # 分钟级验证
            cd = datetime.datetime.now().strftime("%Y%m%d%H%M")
            if cd > ed:
                return False
        else:
            # 天级验证
            cd = datetime.datetime.now().strftime("%Y%m%d")
            if cd > ed:
                return False

        # 验证校验和
        expected_checksum = sum(ord(c) for c in ac) % 256
        if f"{expected_checksum:02X}" != checksum:
            return False

        mc = _x5()
        s1 = hashlib.sha256((mc + entropy + _x1()).encode()).hexdigest()
        s2 = hashlib.md5((s1 + ed).encode()).hexdigest()
        expected = hashlib.sha256((s2 + mc[:16] + _x1()[:4]).encode()).hexdigest().upper()

        return ac == expected
    except:
        return False

# 别名
get_machine_code = _x5
generate_activation_code = _x6
verify_activation_code = _x7

def is_activated():
    """检查是否已激活"""
    try:
        if os.path.exists(_x2()):
            with open(_x2(), "r", encoding='utf-8') as f:
                saved_code = f.read().strip()
            return verify_activation_code(saved_code)
        return False
    except Exception as e:
        logger.error(f"检查激活状态出错: {str(e)}")
        return False

def save_activation_code(code):
    """保存激活码"""
    try:
        with open(_x2(), "w", encoding='utf-8') as f:
            f.write(code)
        return True
    except Exception as e:
        logger.error(f"保存激活码出错: {str(e)}")
        return False

def prompt_for_activation():
    """显示激活界面"""
    root = tk.Tk()
    root.title("软件激活 - 图片处理工具")
    root.geometry("500x400")
    root.resizable(False, False)

    # 居中显示
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (500 // 2)
    y = (root.winfo_screenheight() // 2) - (400 // 2)
    root.geometry(f"500x400+{x}+{y}")

    machine_code = get_machine_code()

    # 标题
    title_label = ttk.Label(root, text="图片处理工具 - 软件激活", font=("Arial", 14, "bold"))
    title_label.pack(pady=20)

    # 机器码显示
    ttk.Label(root, text="机器码（请发送给作者获取激活码）:", font=("Arial", 10)).pack(pady=(10, 5))

    machine_frame = ttk.Frame(root)
    machine_frame.pack(pady=5, padx=20, fill='x')

    machine_code_var = tk.StringVar(value=machine_code)
    machine_entry = ttk.Entry(machine_frame, textvariable=machine_code_var, state='readonly', font=("Courier", 10))
    machine_entry.pack(side='left', fill='x', expand=True)

    def copy_machine_code():
        if pyperclip:
            pyperclip.copy(machine_code)
            messagebox.showinfo("提示", "机器码已复制到剪贴板！")
        else:
            # 手动选择文本
            machine_entry.config(state='normal')
            machine_entry.select_range(0, 'end')
            machine_entry.config(state='readonly')
            messagebox.showinfo("提示", "请手动复制机器码")

    copy_btn = ttk.Button(machine_frame, text="复制", command=copy_machine_code, width=8)
    copy_btn.pack(side='right', padx=(5, 0))

    # 激活码输入
    ttk.Label(root, text="请输入激活码:", font=("Arial", 10)).pack(pady=(20, 5))

    code_frame = ttk.Frame(root)
    code_frame.pack(pady=5, padx=20, fill='x')

    code_entry = ttk.Entry(code_frame, font=("Courier", 10))
    code_entry.pack(fill='x')
    code_entry.focus()

    # 按钮区域
    button_frame = ttk.Frame(root)
    button_frame.pack(pady=20)

    def activate():
        activation_code = code_entry.get().strip()
        if not activation_code:
            messagebox.showerror("错误", "请输入激活码！")
            return

        if verify_activation_code(activation_code):
            if save_activation_code(activation_code):
                messagebox.showinfo("成功", "激活成功！程序即将启动。")
                root.destroy()
            else:
                messagebox.showerror("错误", "保存激活码失败！")
        else:
            messagebox.showerror("错误", "激活码无效或已过期！\n请检查激活码是否正确。")

    def exit_app():
        root.destroy()
        sys.exit(0)

    activate_btn = ttk.Button(button_frame, text="激活", command=activate, width=12)
    activate_btn.pack(side='left', padx=5)

    exit_btn = ttk.Button(button_frame, text="退出", command=exit_app, width=12)
    exit_btn.pack(side='left', padx=5)

    # 绑定回车键
    code_entry.bind('<Return>', lambda e: activate())

    # 信息说明
    info_text = """使用说明：
1. 复制上方机器码
2. 发送机器码给作者获取激活码
3. 输入激活码并点击激活
4. 激活成功后即可使用软件

注意：激活码与本机绑定，请妥善保管"""

    info_label = ttk.Label(root, text=info_text, font=("Arial", 9), foreground="gray")
    info_label.pack(pady=10)

    root.protocol("WM_DELETE_WINDOW", exit_app)
    root.mainloop()

# 检查激活状态
if not is_activated():
    prompt_for_activation()
# ==================== 激活验证系统结束 ====================

# 简化的验证码登录对话框，用户手动输入验证码

class DoubaoLoginDialog(QDialog):
    """豆包AI登录弹窗 """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_browser = 0
        self.phone_number = ""
        self.verification_code = ""
        self.driver = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("AI登录")
        self.setModal(True)
        self.resize(350, 200)

        # 居中显示
        if self.parent():
            parent_geo = self.parent().geometry()
            x = parent_geo.x() + (parent_geo.width() - 350) // 2
            y = parent_geo.y() + (parent_geo.height() - 200) // 2
            self.move(x, y)

        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("AI登录")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)

        # 表单布局
        form_layout = QFormLayout()

        # 手机号输入
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("请输入手机号")
        self.phone_input.setStyleSheet("padding: 8px; font-size: 12px;")
        form_layout.addRow("手机号:", self.phone_input)



        layout.addLayout(form_layout)

        # 说明文字
        info_label = QLabel("点击【获取验证码】后，请在浏览器中手动完成验证码输入")
        info_label.setStyleSheet("color: #666; font-size: 11px; margin: 10px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 获取验证码按钮
        self.get_code_btn = QPushButton("获取验证码")
        self.get_code_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                font-size: 12px;
                background-color: #2ed573;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #26d0ce;
            }
            QPushButton:disabled {
                background-color: #ddd;
                color: #999;
            }
        """)
        self.get_code_btn.clicked.connect(self.get_verification_code)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                font-size: 12px;
                background-color: #747d8c;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #57606f;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.get_code_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

    def get_verification_code(self):
        """获取验证码 - 自动化获取，手动输入"""
        phone = self.phone_input.text().strip()
        if not phone:
            QMessageBox.warning(self, "提示", "请先输入手机号！")
            return

        if len(phone) != 11 or not phone.isdigit():
            QMessageBox.warning(self, "提示", "请输入正确的手机号！")
            return

        self.phone_number = phone
        self.get_code_btn.setEnabled(False)
        self.get_code_btn.setText("正在获取...")

        # 启动浏览器并自动获取验证码
        try:
            self._auto_get_verification_code(phone)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取验证码失败: {str(e)}")
            self.get_code_btn.setEnabled(True)
            self.get_code_btn.setText("获取验证码")

    def _auto_get_verification_code(self, phone):
        """自动获取验证码"""
        try:
            # 初始化浏览器
            self._init_browser()

            # 访问豆包AI
            self.driver.get("https://www.doubao.com/")
            time.sleep(3)

            # 查找登录按钮
            login_selectors = [
                "//button[contains(text(), '登录')]",
                "//a[contains(text(), '登录')]",
                "//div[contains(text(), '登录')]",
                "//span[contains(text(), '登录')]",
                "//button[contains(@class, 'login')]",
                "//a[contains(@class, 'login')]"
            ]

            login_btn = None
            for selector in login_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        login_btn = elements[0]
                        break
                except:
                    continue

            if login_btn:
                login_btn.click()
                time.sleep(3)
            else:
                raise Exception("找不到登录按钮")

            # 查找手机号输入框
            phone_selectors = [
                "//input[@placeholder='请输入手机号']",
                "//input[contains(@placeholder, '手机号')]",
                "//input[@type='tel']",
                "//input[contains(@class, 'phone')]"
            ]

            phone_input = None
            for selector in phone_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        phone_input = elements[0]
                        break
                except:
                    continue

            if phone_input:
                phone_input.clear()
                phone_input.send_keys(phone)
                time.sleep(1)
            else:
                raise Exception("找不到手机号输入框")

            # 等待页面加载完成
            time.sleep(3)

            # 查找并勾选同意条款复选框 - 必须先勾选
            agreement_checked = False
            agreement_selectors = [
                # 直接查找复选框
                "//input[@type='checkbox']",
                # 查找包含同意文本的复选框区域
                "//div[contains(text(), '已阅读并同意')]/..//input[@type='checkbox']",
                "//div[contains(text(), '使用协议')]/..//input[@type='checkbox']",
                "//div[contains(text(), '隐私政策')]/..//input[@type='checkbox']",
                "//label[contains(text(), '同意')]//input[@type='checkbox']",
                # 查找可点击的复选框区域
                "//div[contains(@class, 'checkbox')]",
                "//span[contains(@class, 'checkbox')]",
                # 查找复选框的父容器
                "//div[contains(text(), '已阅读并同意')]",
                "//div[contains(text(), '使用协议') and contains(text(), '隐私政策')]"
            ]

            for selector in agreement_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        element = elements[0]
                        # 尝试多种点击方式
                        try:
                            # 方式1：直接点击
                            element.click()
                            agreement_checked = True
                            break
                        except:
                            try:
                                # 方式2：JavaScript点击
                                self.driver.execute_script("arguments[0].click();", element)
                                agreement_checked = True
                                break
                            except:
                                try:
                                    # 方式3：点击父容器
                                    parent = element.find_element(By.XPATH, "./..")
                                    parent.click()
                                    agreement_checked = True
                                    break
                                except:
                                    continue
                except:
                    continue

            if not agreement_checked:
                raise Exception("无法勾选同意条款复选框")

            # 等待勾选生效
            time.sleep(1)

            # 查找获取验证码按钮（下一步按钮）
            code_btn_selectors = [
                "//button[contains(text(), '下一步')]",
                "//button[contains(text(), '获取验证码')]",
                "//button[contains(text(), '发送验证码')]",
                "//div[contains(text(), '下一步')]",
                "//div[contains(text(), '获取验证码')]",
                "//span[contains(text(), '下一步')]",
                "//span[contains(text(), '获取验证码')]"
            ]

            code_btn = None
            for selector in code_btn_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        for element in elements:
                            if element.is_enabled() and element.is_displayed():
                                code_btn = element
                                break
                        if code_btn:
                            break
                except:
                    continue

            if code_btn:
                # 确保按钮可点击
                time.sleep(1)
                code_btn.click()
                time.sleep(2)

                # 显示成功信息和手动输入提示
                self.get_code_btn.setText("已发送")
                QMessageBox.information(self, "验证码已发送",
                    f"验证码已发送到手机 {phone}\n\n请在浏览器中手动输入验证码并点击登录\n登录成功后可关闭此对话框")

                # 设置占位符验证码
                self.verification_code = "手动输入"

                # 等待用户手动完成登录后关闭对话框
                self.accept()
            else:
                raise Exception("找不到获取验证码按钮")

        except Exception as e:
            raise Exception(f"自动获取验证码失败: {str(e)}")

    def _init_browser(self):
        """初始化浏览器"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager

            options = Options()

            # 使用用户数据目录
            import os
            current_dir = os.getcwd()
            user_data_dir = os.path.join(current_dir, f"chrome_user_data_doubao_{self.selected_browser}")
            os.makedirs(user_data_dir, exist_ok=True)

            options.add_argument(f'--user-data-dir={user_data_dir}')
            options.add_argument('--profile-directory=Default')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # 登录时使用有头模式但最小化窗口，保证登录记忆正常
            # options.add_argument('--headless=new')  # 不使用无头模式
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument('--window-size=800,600')  # 保持这个大小便于用户操作

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)

            # 反检测设置
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        except Exception as e:
            raise Exception(f"初始化浏览器失败: {str(e)}")

    def closeEvent(self, event):
        """关闭事件"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        event.accept()

    def get_login_info(self):
        """获取登录信息"""
        return self.phone_number, self.verification_code







    def auto_input_verification_code(self, code):
        """简化的验证码输入 - 不自动输入，提示用户手动输入"""
        try:
            logger.info(f"用户输入验证码: {code}")
            # 简化：不自动输入验证码，只提示用户手动操作
            QMessageBox.information(self, "验证码输入",
                f"验证码: {code}\n\n请在浏览器中手动输入此验证码并点击登录")
            return True
        except Exception as e:
            logger.error(f"验证码处理失败: {str(e)}")


    def auto_click_login_button(self):
        """自动点击登录按钮 - 豆包AI会自动登录"""
        try:
            # 豆包AI在输入完验证码后会自动登录，不需要点击登录按钮
            logger.info("豆包AI验证码输入完成，等待自动登录...")
            self.status_label.setText("验证码已输入，等待自动登录...")

            # 等待自动登录完成
            time.sleep(5)

            # 检查是否还在验证码页面，如果是则可能需要手动点击
            current_url = self.driver.current_url
            page_source = self.driver.page_source

            # 如果页面还显示验证码相关内容，尝试查找登录按钮
            if "验证码" in page_source or "输入 6 位" in page_source:
                logger.info("仍在验证码页面，尝试查找登录按钮...")

                # 查找登录按钮
                login_selectors = [
                    "//button[contains(text(), '登录')]",
                    "//button[contains(text(), '登陆')]",
                    "//button[contains(text(), '确认')]",
                    "//button[contains(text(), '提交')]",
                    "//input[@type='submit']",
                    "//button[@type='submit']"
                ]

                for selector in login_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        if elements:
                            for element in elements:
                                if element.is_displayed() and element.is_enabled():
                                    element.click()
                                    logger.info("手动点击登录按钮成功")
                                    self.status_label.setText("已点击登录按钮，请等待登录完成")
                                    time.sleep(3)
                                    break
                    except:
                        continue
            else:
                logger.info("豆包AI自动登录成功")
                self.status_label.setText("自动登录成功")

            # 等待登录完成后关闭对话框
            QTimer.singleShot(3000, self.accept)
            return True

        except Exception as e:
            logger.error(f"登录处理失败: {str(e)}")
            self.status_label.setText(f"登录处理失败: {str(e)}")
            # 仍然关闭对话框
            QTimer.singleShot(3000, self.accept)
            return False

    def closeEvent(self, event):
        """关闭事件"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        event.accept()

class ParallelProcessor:
    """并行处理管理器"""

    def __init__(self, max_doubao_workers=2, max_removal_workers=2):
        self.max_doubao_workers = max_doubao_workers
        self.max_removal_workers = max_removal_workers

        # 任务队列
        self.doubao_queue = queue.Queue()
        self.removal_queue = queue.Queue()
        self.crop_queue = queue.Queue()

        # 结果队列
        self.completed_queue = queue.Queue()

        # 线程池（设置足够大的线程池）
        self.doubao_executor = ThreadPoolExecutor(max_workers=3)  # 支持3个豆包AI线程
        self.removal_executor = ThreadPoolExecutor(max_workers=max_removal_workers)
        self.crop_executor = ThreadPoolExecutor(max_workers=2)

        # 控制标志
        self.is_running = True

        # 统计
        self.total_images = 0
        self.completed_count = 0

    def start_processing(self, image_files, process_type, output_folder, progress_callback, enable_bg_removal=True):
        """开始并行处理"""
        try:
            self.total_images = len(image_files)
            self.completed_count = 0
            self.enable_bg_removal = enable_bg_removal

            # 添加豆包AI任务到队列
            for image_path in image_files:
                self.doubao_queue.put((image_path, process_type, output_folder))

            # 启动豆包AI工作线程（单浏览器多网页模式）
            doubao_workers = 1
            logger.info(f"启动 {doubao_workers} 个豆包AI工作线程（单浏览器多网页模式）")
            for i in range(doubao_workers):
                try:
                    future = self.doubao_executor.submit(self._doubao_multi_tab_worker, i, progress_callback, image_files, process_type, output_folder)
                    logger.info(f"豆包AI工作线程 {i} 提交成功")
                except Exception as e:
                    logger.error(f"提交豆包AI工作线程 {i} 失败: {str(e)}")

            # 只有启用去背景时才启动Removal.AI工作线程（减少线程数量以提高稳定性）
            if self.enable_bg_removal:
                logger.info("启动 2 个Removal.AI工作线程（减少资源占用）")
                for i in range(2):  # 减少到2个线程
                    try:
                        future = self.removal_executor.submit(self._removal_worker, i, progress_callback)
                        logger.info(f"Removal.AI工作线程 {i} 提交成功")
                    except Exception as e:
                        logger.error(f"提交Removal.AI工作线程 {i} 失败: {str(e)}")

                # 启动裁剪工作线程
                logger.info("启动 1 个裁剪工作线程（减少资源占用）")
                for i in range(1):  # 减少到1个线程
                    try:
                        future = self.crop_executor.submit(self._crop_worker, i, progress_callback)
                        logger.info(f"裁剪工作线程 {i} 提交成功")
                    except Exception as e:
                        logger.error(f"提交裁剪工作线程 {i} 失败: {str(e)}")
            else:
                logger.info("跳过Removal.AI和裁剪线程（未启用去背景）")

        except Exception as e:
            logger.error(f"启动并行处理失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def _doubao_multi_tab_worker(self, worker_id, progress_callback, image_files, process_type, output_folder):
        """豆包AI多网页工作线程 - 单浏览器多网页模式"""
        try:
            logger.info(f"豆包AI多网页工作线程 {worker_id} 启动")

            # 创建DoubaoWorker实例来处理多网页，传递去背景选项
            worker = DoubaoWorker(worker_id, image_files, process_type, output_folder, self.enable_bg_removal)

            # 设置完成队列引用，让worker可以向队列添加结果
            worker.completed_queue = self.completed_queue
            worker.enable_bg_removal = self.enable_bg_removal
            worker.removal_queue = self.removal_queue if self.enable_bg_removal else None

            # 移除跨线程的进度回调以避免UI线程冲突
            # if hasattr(self, 'main_window_progress_callback'):
            #     worker.progress_callback = self.main_window_progress_callback

            # 连接信号
            worker.progress_updated.connect(lambda progress, status, detail: progress_callback(status))
            worker.process_complete.connect(lambda results: logger.info(f"豆包AI-{worker_id} 处理完成，共处理 {len(results)} 张图片"))
            worker.error_occurred.connect(lambda error: logger.error(f"豆包AI-{worker_id} 发生错误: {error}"))

            # 启动处理（作为QThread）
            worker.start()

            # 等待处理完成
            worker.wait()

            logger.info(f"豆包AI多网页工作线程 {worker_id} 完成")

        except Exception as e:
            logger.error(f"豆包AI多网页工作线程 {worker_id} 发生严重错误: {str(e)}")
            import traceback
            traceback.print_exc()

            # 确保清理资源
            try:
                if 'worker' in locals() and hasattr(worker, 'driver') and worker.driver:
                    worker.driver.quit()
            except:
                pass

    def _doubao_worker(self, worker_id, progress_callback):
        """豆包AI工作线程 - 每张图片使用新的浏览器"""
        logger.info(f"豆包AI工作线程 {worker_id} 启动")

        while self.is_running:
            try:
                # 获取任务
                task = self.doubao_queue.get(timeout=1)
                if task is None:
                    break

                image_path, process_type, output_folder = task
                file_name = os.path.basename(image_path)

                progress_callback(f"豆包AI-{worker_id} 处理中: {file_name}")

                # 为每张图片创建新的处理器和浏览器
                doubao_processor = DoubaoSingleProcessor(worker_id)

                try:
                    # 初始化独立的浏览器
                    doubao_processor.init_browser()

                    # 直接访问豆包AI，不主动登录
                    doubao_url = "https://www.doubao.com/chat/"
                    logger.info(f"豆包AI-{worker_id} 为图片 {file_name} 访问: {doubao_url}")
                    doubao_processor.driver.get(doubao_url)

                    # 等待页面加载（优化速度）
                    logger.info(f"豆包AI-{worker_id} 等待页面加载")
                    time.sleep(2)  # 减少等待时间从5秒到2秒

                    # 检查页面状态，只记录信息，不主动登录
                    page_source = doubao_processor.driver.page_source
                    if "获取验证码" in page_source or "输入手机号" in page_source or "验证码" in page_source:
                        logger.info(f"豆包AI-{worker_id} 页面显示需要验证码，但不主动登录")
                        logger.info(f"豆包AI-{worker_id} 如果需要登录，豆包AI会自动提示")
                    else:
                        logger.info(f"豆包AI-{worker_id} 页面正常，可以直接使用")

                    # 处理图片
                    result_url = doubao_processor.process_image(image_path, process_type)

                    if result_url:
                        # 下载图片
                        temp_path = doubao_processor.download_image(result_url, image_path, output_folder)
                        if temp_path:
                            # 根据选项决定后续处理
                            if self.enable_bg_removal:
                                # 添加到Removal.AI队列
                                self.removal_queue.put((temp_path, image_path))
                                progress_callback(f"去背景: {file_name}")
                            else:
                                # 直接重命名完成
                                final_path = self._rename_final_file(temp_path, image_path)
                                if final_path:
                                    self.completed_count += 1
                                    self.completed_queue.put((final_path, True))
                                    progress_callback(f"完成: {file_name}")
                                    logger.info(f"豆包AI直接完成，添加到完成队列: {file_name}")
                        else:
                            progress_callback(f"下载失败: {file_name}")
                    else:
                        progress_callback(f"处理失败: {file_name}")

                finally:
                    # 处理完一张图片后立即关闭浏览器
                    if doubao_processor.driver:
                        try:
                            doubao_processor.driver.quit()
                            logger.info(f"豆包AI-{worker_id} 图片 {file_name} 处理完成，浏览器已关闭")
                        except:
                            logger.warning(f"豆包AI-{worker_id} 浏览器关闭时出错，忽略")

                # 标记任务完成
                self.doubao_queue.task_done()

            except queue.Empty:
                time.sleep(0.1)  # 减少等待时间，提高响应速度
                continue
            except Exception as e:
                logger.error(f"豆包AI工作线程 {worker_id} 出错: {str(e)}")
                logger.error(f"豆包AI工作线程 {worker_id} 错误详情: {type(e).__name__}")

                # 即使出错也要标记任务完成，避免队列卡住
                try:
                    self.doubao_queue.task_done()
                except:
                    pass

                # 清理可能的浏览器实例
                try:
                    if 'doubao_processor' in locals() and doubao_processor.driver:
                        doubao_processor.driver.quit()
                        logger.info(f"豆包AI工作线程 {worker_id} 清理浏览器实例")
                except:
                    pass

                time.sleep(1)  # 出错后稍微等待，但继续运行
                logger.info(f"豆包AI工作线程 {worker_id} 从错误中恢复，继续处理下一个任务")

        logger.info(f"豆包AI工作线程 {worker_id} 退出")

    def _removal_worker(self, worker_id, progress_callback):
        """Removal.AI工作线程"""
        logger.info(f"Removal.AI工作线程 {worker_id} 启动")

        # 创建独立的浏览器实例
        removal_processor = RemovalAIProcessor(worker_id)

        try:
            while self.is_running:
                try:
                    # 获取任务
                    task = self.removal_queue.get(timeout=1)
                    if task is None:
                        break

                    temp_path, original_path = task
                    file_name = os.path.basename(original_path)

                    progress_callback(f"去背景: {file_name}")

                    # 去背景处理
                    success = removal_processor.process_image(temp_path)

                    if success:
                        # 添加到裁剪队列（去背景模式总是包含裁剪）
                        self.crop_queue.put((temp_path, original_path))
                        progress_callback(f"裁剪: {file_name}")
                    else:
                        progress_callback(f"去背景失败: {file_name}")
                        # 即使失败也进行裁剪
                        self.crop_queue.put((temp_path, original_path))

                    self.removal_queue.task_done()

                    # 处理完一张图片后，刷新页面准备下一张
                    try:
                        removal_processor.driver.execute_script("window.location.reload();")
                        time.sleep(3)
                    except:
                        pass

                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Removal.AI工作线程 {worker_id} 出错: {str(e)}")

        finally:
            removal_processor.cleanup()
            logger.info(f"Removal.AI工作线程 {worker_id} 退出")

    def _crop_worker(self, worker_id, progress_callback):
        """裁剪工作线程"""
        logger.info(f"裁剪工作线程 {worker_id} 启动")

        try:
            while self.is_running:
                try:
                    # 获取任务
                    task = self.crop_queue.get(timeout=1)
                    if task is None:
                        break

                    temp_path, original_path = task

                    # 裁剪和重命名
                    final_path = self._crop_and_rename(temp_path, original_path)

                    if final_path:
                        self.completed_count += 1
                        self.completed_queue.put((final_path, True))
                        file_name = os.path.basename(original_path)
                        progress_callback(f"完成: {file_name}")
                        logger.info(f"裁剪完成，添加到完成队列: {file_name}")
                    else:
                        self.completed_queue.put((temp_path, False))
                        file_name = os.path.basename(original_path)
                        progress_callback(f"裁剪失败: {file_name}")
                        logger.error(f"裁剪失败: {file_name}")

                    self.crop_queue.task_done()

                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"裁剪工作线程 {worker_id} 出错: {str(e)}")

        finally:
            logger.info(f"裁剪工作线程 {worker_id} 退出")

    def _crop_and_rename(self, temp_path, original_path):
        """裁剪透明区域并重命名"""
        try:
            # 裁剪透明区域
            img = Image.open(temp_path).convert("RGBA")
            img_array = np.array(img)
            alpha = img_array[:, :, 3]
            non_transparent = np.where(alpha > 0)

            if len(non_transparent[0]) > 0:
                top = non_transparent[0].min()
                bottom = non_transparent[0].max()
                left = non_transparent[1].min()
                right = non_transparent[1].max()

                cropped_img = img.crop((left, top, right + 1, bottom + 1))
                cropped_img.save(temp_path, "PNG")

                original_size = f"{img.width}x{img.height}"
                cropped_size = f"{cropped_img.width}x{cropped_img.height}"
                logger.info(f"裁剪完成: {original_size} → {cropped_size}")

            # 重命名为原始文件名
            file_name = os.path.basename(original_path)
            base_name, _ = os.path.splitext(file_name)
            save_dir = os.path.dirname(temp_path)
            final_path = os.path.join(save_dir, f"{base_name}.png")

            # 处理文件名冲突
            counter = 1
            while os.path.exists(final_path):
                final_path = os.path.join(save_dir, f"{base_name}_{counter}.png")
                counter += 1

            os.rename(temp_path, final_path)
            return final_path

        except Exception as e:
            logger.error(f"裁剪和重命名失败: {str(e)}")
            return None

    def _rename_final_file(self, temp_path, original_path):
        """重命名为最终文件"""
        try:
            # 获取原始文件名
            file_name = os.path.basename(original_path)
            base_name, _ = os.path.splitext(file_name)
            save_dir = os.path.dirname(temp_path)
            final_path = os.path.join(save_dir, f"{base_name}.png")

            # 处理文件名冲突
            counter = 1
            while os.path.exists(final_path):
                final_path = os.path.join(save_dir, f"{base_name}_{counter}.png")
                counter += 1

            os.rename(temp_path, final_path)
            return final_path

        except Exception as e:
            logger.error(f"重命名文件失败: {str(e)}")
            return None

    def stop(self):
        """停止所有处理"""
        self.is_running = False

        # 关闭线程池
        self.doubao_executor.shutdown(wait=False)
        self.removal_executor.shutdown(wait=False)
        self.crop_executor.shutdown(wait=False)

        # 关闭共享浏览器
        try:
            browser_manager = SharedBrowserManager()
            browser_manager.quit_driver()
        except Exception as e:
            logger.error(f"关闭共享浏览器失败: {str(e)}")

class DoubaoSingleProcessor:
    """单个豆包AI处理器"""

    def __init__(self, worker_id):
        self.worker_id = worker_id
        self.driver = None

    def init_browser(self):
        """初始化独立的浏览器实例"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager

            options = Options()

            # 所有线程都使用浏览器1的用户数据目录
            import os
            current_dir = os.getcwd()
            user_data_dir = os.path.join(current_dir, "chrome_user_data_doubao_1")  # 固定使用浏览器1的路径

            # 确保目录存在
            os.makedirs(user_data_dir, exist_ok=True)

            # 设置用户数据目录和配置文件
            options.add_argument(f'--user-data-dir={user_data_dir}')
            options.add_argument('--profile-directory=Default')

            logger.info(f"豆包AI-{self.worker_id} 使用独立用户数据目录: {user_data_dir}")
            logger.info(f"豆包AI-{self.worker_id} 目录是否存在: {os.path.exists(user_data_dir)}")

            # 优化启动速度的设置
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')  # 禁用GPU加速，提高启动速度
            options.add_argument('--disable-extensions')  # 禁用扩展
            options.add_argument('--disable-plugins')  # 禁用插件
            options.add_argument('--disable-images')  # 禁用图片加载，提高速度
            options.add_argument('--disable-javascript')  # 禁用JS（如果不影响功能）

            # 设置窗口大小和位置（轻微错开位置）
            window_x = 100 + (self.worker_id * 50)  # 轻微错开位置
            window_y = 100 + (self.worker_id * 50)
            # 使用合理的窗口尺寸
            options.add_argument(f'--window-size=1000,600')
            
            # 使用有头模式但移到屏幕外，保证登录记忆正常
            # options.add_argument('--headless=new')  # 不使用无头模式
            
            # 添加必要的参数
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-software-rasterizer')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--no-sandbox')
            options.add_argument('--window-size=1280,800')  # 设置足够大的窗口大小以确保功能正常
            options.add_argument('--window-position=-2000,-2000')  # 将窗口移到屏幕外

            # 禁用一些可能导致问题的功能
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # 允许多个Chrome实例使用同一用户数据目录
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-web-security')
            options.add_argument('--remote-debugging-port=0')  # 使用随机端口避免冲突

            # 设置语言
            options.add_argument('--lang=zh-CN')

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)

            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en']})")

            # 设置用户代理
            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": self.driver.execute_script("return navigator.userAgent;"),
                "platform": "Win32"
            })

            # 等待浏览器稳定
            import time
            time.sleep(2)

            logger.info(f"豆包AI-{self.worker_id} 浏览器初始化成功")
        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 浏览器初始化失败: {str(e)}")
            raise

    def perform_login(self):
        """执行登录流程"""
        try:
            # 访问豆包AI登录页面
            self.driver.get("https://www.doubao.com/")
            time.sleep(3)

            # 查找登录按钮
            login_selectors = [
                "//button[contains(text(), '登录')]",
                "//a[contains(text(), '登录')]",
                "//div[contains(text(), '登录')]",
                "//span[contains(text(), '登录')]",
                "//button[contains(@class, 'login')]",
                "//a[contains(@class, 'login')]"
            ]

            login_btn = None
            for selector in login_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        login_btn = elements[0]
                        break
                except:
                    continue

            if not login_btn:
                logger.error(f"豆包AI-{self.worker_id} 找不到登录按钮")
                return False

            # 点击登录按钮
            login_btn.click()
            time.sleep(3)

            # 显示登录弹窗
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                dialog = DoubaoLoginDialog(self.worker_id)
                if dialog.exec_() == QDialog.Accepted:
                    phone, code = dialog.get_login_info()

                    if phone and not code:
                        # 只有手机号，需要获取验证码
                        return self._get_verification_code(phone)
                    elif phone and code:
                        # 有手机号和验证码，执行登录
                        return self._login_with_code(phone, code)
                else:
                    return False

            return False

        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 登录流程出错: {str(e)}")
            return False

    def _get_verification_code(self, phone):
        """获取验证码"""
        try:
            # 查找手机号输入框
            phone_selectors = [
                "//input[@placeholder='请输入手机号']",
                "//input[contains(@placeholder, '手机号')]",
                "//input[@type='tel']",
                "//input[contains(@class, 'phone')]"
            ]

            phone_input = None
            for selector in phone_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        phone_input = elements[0]
                        break
                except:
                    continue

            if phone_input:
                # 输入手机号
                phone_input.clear()
                phone_input.send_keys(phone)
                time.sleep(1)

                # 查找获取验证码按钮
                code_btn_selectors = [
                    "//button[contains(text(), '获取验证码')]",
                    "//button[contains(text(), '发送验证码')]",
                    "//div[contains(text(), '获取验证码')]",
                    "//span[contains(text(), '获取验证码')]"
                ]

                code_btn = None
                for selector in code_btn_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        if elements:
                            code_btn = elements[0]
                            break
                    except:
                        continue

                if code_btn:
                    code_btn.click()
                    logger.info(f"豆包AI-{self.worker_id} 已发送验证码到 {phone}")

                    # 再次显示弹窗让用户输入验证码
                    from PyQt5.QtWidgets import QApplication
                    app = QApplication.instance()
                    if app:
                        dialog = DoubaoLoginDialog(self.worker_id)
                        dialog.phone_input.setText(phone)
                        dialog.phone_input.setEnabled(False)
                        dialog.get_code_btn.setEnabled(False)
                        dialog.get_code_btn.setText("已发送")
                        dialog.status_label.setText("验证码已发送，请输入验证码")

                        if dialog.exec_() == QDialog.Accepted:
                            _, code = dialog.get_login_info()
                            if code:
                                return self._login_with_code(phone, code)

                    return False
                else:
                    logger.error(f"豆包AI-{self.worker_id} 找不到获取验证码按钮")
                    return False
            else:
                logger.error(f"豆包AI-{self.worker_id} 找不到手机号输入框")
                return False

        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 获取验证码出错: {str(e)}")
            return False

    def _login_with_code(self, phone, code):
        """使用验证码登录"""
        try:
            # 查找验证码输入框
            code_selectors = [
                "//input[@placeholder='请输入验证码']",
                "//input[contains(@placeholder, '验证码')]",
                "//input[contains(@class, 'code')]",
                "//input[@type='text'][last()]"
            ]

            code_input = None
            for selector in code_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        code_input = elements[0]
                        break
                except:
                    continue

            if code_input:
                # 输入验证码
                code_input.clear()
                code_input.send_keys(code)
                time.sleep(1)

                # 查找登录按钮
                login_selectors = [
                    "//button[contains(text(), '登录')]",
                    "//button[contains(text(), '确认')]",
                    "//div[contains(text(), '登录')]",
                    "//span[contains(text(), '登录')]"
                ]

                login_btn = None
                for selector in login_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        if elements:
                            login_btn = elements[0]
                            break
                    except:
                        continue

                if login_btn:
                    login_btn.click()
                    time.sleep(3)

                    # 检查是否登录成功
                    if self._check_login_success():
                        logger.info(f"豆包AI-{self.worker_id} 登录成功")
                        return True
                    else:
                        logger.error(f"豆包AI-{self.worker_id} 登录失败")
                        return False
                else:
                    logger.error(f"豆包AI-{self.worker_id} 找不到登录按钮")
                    return False
            else:
                logger.error(f"豆包AI-{self.worker_id} 找不到验证码输入框")
                return False

        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 验证码登录出错: {str(e)}")
            return False

    def _check_login_success(self):
        """检查登录是否成功"""
        try:
            # 检查是否有用户头像或用户信息
            success_indicators = [
                "//img[contains(@alt, '头像')]",
                "//div[contains(@class, 'user')]",
                "//div[contains(@class, 'avatar')]",
                "//button[contains(@class, 'user')]"
            ]

            for indicator in success_indicators:
                try:
                    elements = self.driver.find_elements(By.XPATH, indicator)
                    if elements:
                        return True
                except:
                    continue

            # 检查URL是否变化
            current_url = self.driver.current_url
            if "chat" in current_url or "user" in current_url:
                return True

            return False

        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 检查登录状态出错: {str(e)}")
            return False































    def process_image(self, image_path, process_type):
        """处理单张图片"""
        try:
            # 直接开始处理，不检查登录状态
            logger.info(f"豆包AI-{self.worker_id} 开始处理图片: {os.path.basename(image_path)}")

            # 等待页面稳定
            time.sleep(2)

            # 查找文件上传元素
            file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")
            if not file_inputs:
                # 尝试点击上传按钮
                upload_elements = self.driver.find_elements(By.XPATH,
                    "//button[contains(@class, 'upload') or contains(text(), '上传') or contains(@aria-label, '上传')]")
                if upload_elements:
                    # 点击上传按钮
                    time.sleep(1)
                    upload_elements[0].click()
                    time.sleep(1)
                    file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")

            if file_inputs:
                # 上传图片
                logger.info(f"豆包AI-{self.worker_id} 开始上传图片")
                time.sleep(1)
                file_inputs[0].send_keys(image_path)

                # 等待上传完成
                logger.info(f"豆包AI-{self.worker_id} 等待上传完成")
                time.sleep(5)



                # 发送处理指令
                if process_type == "remove_pointer":
                    message = "帮我去掉这张图片中的指针"
                else:
                    message = "帮我去掉这张图片中的遮挡物"

                # 查找输入框并发送消息
                input_selectors = [
                    "//textarea[contains(@placeholder, '输入')]",
                    "//input[contains(@placeholder, '输入')]",
                    "//div[contains(@contenteditable, 'true')]"
                ]

                message_sent = False
                for selector in input_selectors:
                    try:
                        input_elements = self.driver.find_elements(By.XPATH, selector)
                        if input_elements:
                            input_element = input_elements[0]

                            # 输入消息
                            time.sleep(1)
                            input_element.clear()
                            time.sleep(0.5)

                            # 直接输入消息
                            input_element.send_keys(message)
                            time.sleep(1)

                            # 查找发送按钮
                            send_buttons = self.driver.find_elements(By.XPATH,
                                "//button[contains(text(), '发送') or contains(@class, 'send')]")
                            if send_buttons:
                                time.sleep(0.5)
                                send_buttons[0].click()
                                message_sent = True
                                break
                            else:
                                # 尝试按回车键
                                from selenium.webdriver.common.keys import Keys
                                time.sleep(0.5)
                                input_element.send_keys(Keys.RETURN)
                                message_sent = True
                                break
                    except Exception as e:
                        logger.debug(f"豆包AI-{self.worker_id} 输入框尝试失败: {str(e)}")
                        continue

                if not message_sent:
                    logger.error("无法发送处理指令")
                    return None

                logger.info(f"豆包AI-{self.worker_id} 指令已发送，等待AI处理...")

                # 等待豆包AI处理完成
                logger.info(f"豆包AI-{self.worker_id} 等待AI处理 (25秒)")
                time.sleep(25)

                # 查找结果图片
                result_selectors = ["//img[contains(@src, 'http')]"]

                for selector in result_selectors:
                    try:
                        images = self.driver.find_elements(By.XPATH, selector)

                        if len(images) >= 2:  # 至少要有2张图片（原图+结果图）
                            # 更严格的图片筛选
                            candidate_images = []

                            for i, img in enumerate(images):
                                result_url = img.get_attribute("src")
                                if result_url and result_url.startswith("http"):
                                    # 更严格的排除条件
                                    exclude_keywords = ["avatar", "user", "profile", "icon", "head", "face"]
                                    exclude_patterns = ["40x40", "50x50", "60x60", "80x80", "100x100"]

                                    # 检查URL是否包含排除关键词
                                    url_excluded = any(keyword in result_url.lower() for keyword in exclude_keywords)
                                    pattern_excluded = any(pattern in result_url for pattern in exclude_patterns)

                                    if not url_excluded and not pattern_excluded:
                                        try:
                                            # 获取图片尺寸
                                            width = int(img.get_attribute("naturalWidth") or img.get_attribute("width") or "0")
                                            height = int(img.get_attribute("naturalHeight") or img.get_attribute("height") or "0")

                                            # 排除小尺寸图片（通常是头像）
                                            if width > 150 and height > 150:  # 至少150x150
                                                candidate_images.append({
                                                    'url': result_url,
                                                    'width': width,
                                                    'height': height,
                                                    'size': width * height,
                                                    'index': i
                                                })
                                        except:
                                            # 如果无法获取尺寸，但URL很长，也考虑
                                            if len(result_url) > 80:
                                                candidate_images.append({
                                                    'url': result_url,
                                                    'width': 0,
                                                    'height': 0,
                                                    'size': 0,
                                                    'index': i
                                                })

                            if candidate_images:
                                # 选择最大的图片
                                best_candidate = max(candidate_images, key=lambda x: x['size'])
                                return best_candidate['url']
                    except Exception as e:
                        logger.error(f"豆包AI-{self.worker_id} 查找图片出错: {str(e)}")
                        continue

                logger.error("未找到处理结果图片")
                return None
            else:
                logger.error("找不到文件上传元素")
                return None

        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 处理图片出错: {str(e)}")
            return None

    def download_image(self, url, original_path, output_folder):
        """下载图片"""
        try:
            import urllib.request

            file_name = os.path.basename(original_path)
            base_name, _ = os.path.splitext(file_name)

            if output_folder:
                save_dir = output_folder
            else:
                original_dir = os.path.dirname(original_path)
                save_dir = os.path.join(original_dir, "processed")

            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            temp_path = os.path.join(save_dir, f"{base_name}_temp.png")
            urllib.request.urlretrieve(url, temp_path)
            return temp_path

        except Exception as e:
            logger.error(f"下载图片出错: {str(e)}")
            return None

    def cleanup(self):
        """清理资源"""
        # 不再需要关闭浏览器，因为使用的是共享浏览器
        # 标签页的关闭由SharedBrowserManager处理
        pass

class RemovalAIProcessor:
    """单个Removal.AI处理器"""

    def __init__(self, worker_id):
        self.worker_id = worker_id
        self.driver = None
        self._setup_driver()

    def _setup_driver(self):
        """设置浏览器"""
        try:
            options = Options()
            # 去背景使用无头模式
            use_headless = True  # 使用无头模式
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument('--headless=new')  # 使用无头模式
            options.add_argument('--window-size=1280,800')  # 设置足够大的窗口大小
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            logger.info(f"Removal.AI-{self.worker_id} 浏览器初始化成功")
        except Exception as e:
            logger.error(f"Removal.AI-{self.worker_id} 浏览器初始化失败: {str(e)}")
            raise

    def process_image(self, image_path):
        """处理图片去背景"""
        try:
            # 打开Removal.AI网站
            self.driver.get("https://removal.ai/")
            time.sleep(5)

            # 查找上传按钮
            upload_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
            )

            # 上传文件
            upload_input.send_keys(image_path)
            logger.info(f"Removal.AI-{self.worker_id} 图片已上传: {os.path.basename(image_path)}")

            # 等待处理完成（优化等待时间）
            time.sleep(25)

            # 查找下载按钮
            download_selectors = [
                "//a[contains(text(), 'Download')]",
                "//button[contains(text(), 'Download')]",
                "//a[contains(@class, 'download')]",
                "//button[contains(@class, 'download')]"
            ]

            download_btn = None
            for selector in download_selectors:
                try:
                    download_btn = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except:
                    continue

            if download_btn:
                download_url = download_btn.get_attribute('href')
                if download_url:
                    response = requests.get(download_url, timeout=30)
                    if response.status_code == 200:
                        with open(image_path, 'wb') as f:
                            f.write(response.content)
                        logger.info(f"Removal.AI-{self.worker_id} 处理完成: {os.path.basename(image_path)}")
                        return True
                    else:
                        logger.error(f"Removal.AI-{self.worker_id} 下载失败: HTTP {response.status_code}")
                        return False
                else:
                    logger.error(f"Removal.AI-{self.worker_id} 无法获取下载链接")
                    return False
            else:
                logger.error(f"Removal.AI-{self.worker_id} 找不到下载按钮")
                return False

        except Exception as e:
            logger.error(f"Removal.AI-{self.worker_id} 处理出错: {str(e)}")
            return False

    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()

class SharedBrowserManager:
    """共享浏览器管理器"""
    _instance = None
    _driver = None
    _tab_handles = []
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def get_driver(self):
        """获取共享的浏览器实例"""
        with self._lock:
            if self._driver is None:
                self._setup_shared_driver()
            return self._driver

    def _setup_shared_driver(self):
        """设置共享浏览器"""
        try:
            options = Options()
            # 共享浏览器使用有头模式但移到屏幕外，保证登录记忆正常
            use_headless = False  # 设置为False保证登录记忆正常
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-extensions')
            options.add_argument('--window-size=1280,800')  # 设置足够大的窗口大小以确保功能正常
            options.add_argument('--window-position=-2000,-2000')  # 将窗口移到屏幕外
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # 添加用户数据目录以保持登录状态
            import tempfile
            user_data_dir = os.path.join(tempfile.gettempdir(), "doubao_shared_chrome_profile")
            options.add_argument(f'--user-data-dir={user_data_dir}')
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            service = Service(ChromeDriverManager().install())
            self._driver = webdriver.Chrome(service=service, options=options)
            self._driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self._driver.set_page_load_timeout(30)

            # 加载保存的登录数据
            self._load_saved_login_data()

            logger.info("共享浏览器初始化成功")
        except Exception as e:
            logger.error(f"共享浏览器初始化失败: {str(e)}")
            raise

    def _load_saved_login_data(self):
        """加载保存的登录数据"""
        try:
            login_file = os.path.join(os.path.dirname(__file__), "doubao_login.json")
            if not os.path.exists(login_file):
                logger.info("共享浏览器: 没有保存的登录数据")
                return False

            with open(login_file, 'r', encoding='utf-8') as f:
                import json
                login_data = json.load(f)

            if not login_data.get("login_success"):
                logger.info("共享浏览器: 登录数据无效")
                return False

            cookies = login_data.get("cookies", [])
            local_storage = login_data.get("local_storage", {})

            if not cookies and not local_storage:
                logger.info("共享浏览器: 没有可用的浏览器数据")
                return False

            # 先访问豆包AI主页
            self._driver.get("https://www.doubao.com/")
            time.sleep(3)

            # 加载Cookie
            if cookies:
                for cookie in cookies:
                    try:
                        clean_cookie = {
                            'name': cookie['name'],
                            'value': cookie['value'],
                            'domain': cookie.get('domain', '.doubao.com'),
                            'path': cookie.get('path', '/'),
                        }
                        if 'secure' in cookie:
                            clean_cookie['secure'] = cookie['secure']
                        if 'httpOnly' in cookie:
                            clean_cookie['httpOnly'] = cookie['httpOnly']

                        self._driver.add_cookie(clean_cookie)
                    except Exception as e:
                        logger.debug(f"共享浏览器: 加载Cookie失败: {str(e)}")

                logger.info(f"共享浏览器: 已加载 {len(cookies)} 个Cookie")

            # 加载LocalStorage
            if local_storage:
                try:
                    storage_script = ""
                    for key, value in local_storage.items():
                        safe_key = str(key).replace("'", "\\'").replace('"', '\\"')
                        safe_value = str(value).replace("'", "\\'").replace('"', '\\"')
                        storage_script += f"localStorage.setItem('{safe_key}', '{safe_value}');\n"

                    if storage_script:
                        self._driver.execute_script(storage_script)
                        logger.info(f"共享浏览器: 已加载 {len(local_storage)} 个Storage项")
                except Exception as e:
                    logger.warning(f"共享浏览器: 加载LocalStorage失败: {str(e)}")

            # 刷新页面应用数据
            self._driver.refresh()
            time.sleep(3)

            logger.info("共享浏览器: 登录数据加载完成")
            return True

        except Exception as e:
            logger.error(f"共享浏览器: 加载登录数据失败: {str(e)}")
            return False

    def create_new_tab(self, worker_id):
        """为工作线程创建新标签页"""
        with self._lock:
            try:
                # 打开新标签页
                self._driver.execute_script("window.open('about:blank', '_blank');")

                # 获取所有窗口句柄
                all_handles = self._driver.window_handles

                # 找到新的标签页句柄
                new_handle = None
                for handle in all_handles:
                    if handle not in self._tab_handles:
                        new_handle = handle
                        break

                if new_handle:
                    self._tab_handles.append(new_handle)
                    logger.info(f"豆包AI-{worker_id}: 创建新标签页 {new_handle}")
                    return new_handle
                else:
                    logger.error(f"豆包AI-{worker_id}: 无法创建新标签页")
                    return None

            except Exception as e:
                logger.error(f"豆包AI-{worker_id}: 创建标签页失败: {str(e)}")
                return None

    def switch_to_tab(self, tab_handle):
        """切换到指定标签页"""
        with self._lock:
            try:
                self._driver.switch_to.window(tab_handle)
                return True
            except Exception as e:
                logger.error(f"切换标签页失败: {str(e)}")
                return False

    def close_tab(self, tab_handle, worker_id):
        """关闭指定标签页"""
        with self._lock:
            try:
                if tab_handle in self._tab_handles:
                    self._driver.switch_to.window(tab_handle)
                    self._driver.close()
                    self._tab_handles.remove(tab_handle)
                    logger.info(f"豆包AI-{worker_id}: 关闭标签页 {tab_handle}")

                    # 如果还有其他标签页，切换到第一个
                    if self._tab_handles:
                        self._driver.switch_to.window(self._tab_handles[0])
                    elif len(self._driver.window_handles) > 0:
                        self._driver.switch_to.window(self._driver.window_handles[0])

            except Exception as e:
                logger.error(f"豆包AI-{worker_id}: 关闭标签页失败: {str(e)}")

    def quit_driver(self):
        """关闭共享浏览器"""
        with self._lock:
            if self._driver:
                try:
                    self._driver.quit()
                    self._driver = None
                    self._tab_handles = []
                    logger.info("共享浏览器已关闭")
                except Exception as e:
                    logger.error(f"关闭共享浏览器失败: {str(e)}")

class DoubaoWorker(QThread):
    """豆包AI处理线程"""
    progress_updated = pyqtSignal(int, str, str)
    process_complete = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, worker_id, image_files, process_type, output_folder=None, enable_bg_removal=False):
        super().__init__()
        self.worker_id = worker_id
        self.image_files = image_files
        self.process_type = process_type  # "remove_pointer" 或 "remove_obstruction"
        self.output_folder = output_folder
        self.enable_bg_removal = enable_bg_removal  # 是否启用去背景
        self.driver = None
        self.processed_images = []
        self.is_running = True

        # 初始化队列引用（将在_doubao_multi_tab_worker中设置）
        self.completed_queue = None
        self.removal_queue = None
        self.progress_callback = None

    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            if psutil:
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                logger.info(f"豆包AI-{self.worker_id} 内存使用: {memory_mb:.1f}MB")

                # 如果内存使用超过1GB，强制垃圾回收
                if memory_mb > 1024:
                    logger.warning(f"豆包AI-{self.worker_id} 内存使用过高，执行垃圾回收")
                    gc.collect()
        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 检查内存使用出错: {str(e)}")

    def run(self):
        try:
            logger.info(f"豆包AI-{self.worker_id} 开始运行")
            self._check_memory_usage()

            # 初始化独立的浏览器实例
            self._init_browser()
            logger.info(f"豆包AI-{self.worker_id} 浏览器初始化完成")
            self._check_memory_usage()

            # 处理所有图片
            self._process_images()
            logger.info(f"豆包AI-{self.worker_id} 图片处理完成")
            self._check_memory_usage()

        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 处理过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(f"豆包AI-{self.worker_id} 处理过程中发生错误: {str(e)}")
        finally:
            # 关闭浏览器
            try:
                if self.driver:
                    logger.info(f"豆包AI-{self.worker_id} 正在关闭浏览器")
                    self.driver.quit()
                    self.driver = None
                    logger.info(f"豆包AI-{self.worker_id} 浏览器已关闭")
            except Exception as e:
                logger.error(f"豆包AI-{self.worker_id} 关闭浏览器时出错: {str(e)}")

            # 强制垃圾回收
            try:
                gc.collect()
                logger.info(f"豆包AI-{self.worker_id} 垃圾回收完成")
            except Exception as e:
                logger.error(f"豆包AI-{self.worker_id} 垃圾回收出错: {str(e)}")

            try:
                self.process_complete.emit(self.processed_images)
                logger.info(f"豆包AI-{self.worker_id} 运行完成")
            except Exception as e:
                logger.error(f"豆包AI-{self.worker_id} 发送完成信号时出错: {str(e)}")

    def _init_browser(self):
        """初始化浏览器实例 - 使用本地Chrome原始配置文件"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service

            options = Options()

            # 每个线程使用独立的用户数据目录
            import os
            current_dir = os.getcwd()
            user_data_dir = os.path.join(current_dir, f"chrome_user_data_doubao_{self.worker_id}")

            # 确保目录存在
            os.makedirs(user_data_dir, exist_ok=True)

            # 设置用户数据目录和配置文件
            options.add_argument(f'--user-data-dir={user_data_dir}')
            options.add_argument('--profile-directory=Default')

            logger.info(f"豆包AI-{self.worker_id} 使用独立用户数据目录: {user_data_dir}")

            # 基础设置（保持最少参数，避免冲突）
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')

            # 设置窗口大小和位置（轻微错开位置）
            window_x = 100 + (self.worker_id * 50)  # 轻微错开位置
            window_y = 100 + (self.worker_id * 50)
            options.add_argument(f'--window-size=800,600')  # 设置合理的窗口大小
            options.add_argument(f'--window-position={window_x},{window_y}')  # 正常位置
            
            # 使用有头模式但移到屏幕外，保证登录记忆正常
            use_headless = False  # 不使用无头模式
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-software-rasterizer')
            options.add_argument('--window-size=1280,800')  # 设置足够大的窗口大小以确保功能正常
            options.add_argument('--window-position=-2000,-2000')  # 将窗口移到屏幕外

            # 禁用一些可能导致问题的功能
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # 允许多个Chrome实例使用同一用户数据目录
            options.add_argument('--disable-features=VizDisplayCompositor')

            # 重要：允许多个Chrome实例共享用户数据目录
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--remote-debugging-port=0')  # 使用随机端口避免冲突

            # 设置语言
            options.add_argument('--lang=zh-CN')

            # 尝试找到本地Chrome可执行文件
            import os
            username = os.getenv('USERNAME', 'Administrator')
            chrome_paths = [
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
                f"C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe"
            ]

            chrome_binary = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_binary = path
                    logger.info(f"豆包AI-{self.worker_id} 找到本地Chrome: {chrome_binary}")
                    break

            if chrome_binary:
                options.binary_location = chrome_binary
            else:
                logger.warning(f"豆包AI-{self.worker_id} 未找到本地Chrome，将使用默认Chrome")

            # 使用ChromeDriverManager获取驱动
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)

            # 设置标准超时
            self.driver.implicitly_wait(10)

            # 不需要最大化窗口，因为窗口已经移到屏幕外
            # self.driver.maximize_window()

            logger.info(f"豆包AI-{self.worker_id} 浏览器初始化成功 (正常模式)")
        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 浏览器初始化失败: {str(e)}")
            raise

    def _wait_for_user_input(self, prompt_message):
        """等待用户输入 - 用于手机验证码等"""
        try:
            from PyQt5.QtWidgets import QInputDialog, QApplication

            # 查找手机号登录选项
            phone_login_selectors = [
                "//div[contains(text(), '手机号')]",
                "//button[contains(text(), '手机号')]",
                "//a[contains(text(), '手机号')]",
                "//span[contains(text(), '手机号')]"
            ]

            phone_login_found = False
            for selector in phone_login_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements and elements[0].is_displayed():
                        elements[0].click()
                        phone_login_found = True
                        logger.info(f"豆包AI-{self.worker_id} 选择手机号登录")
                        time.sleep(2)
                        break
                except:
                    continue

            # 如果没有找到手机号选项，使用邮箱登录
            if not phone_login_found:
                logger.info(f"豆包AI-{self.worker_id} 使用邮箱登录")

                # 输入邮箱
                email_input = self.driver.find_elements(By.XPATH, "//input[@type='email']")
                if email_input:
                    email_input[0].clear()
                    email_input[0].send_keys("<EMAIL>")
                    time.sleep(1)

                    # 点击下一步
                    next_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Next') or contains(text(), '下一步') or @id='identifierNext']")
                    if next_buttons:
                        next_buttons[0].click()
                        time.sleep(3)
                        logger.info(f"豆包AI-{self.worker_id} 邮箱输入完成")

                # 输入密码
                password_input = self.driver.find_elements(By.XPATH, "//input[@type='password']")
                if password_input:
                    password_input[0].clear()
                    password_input[0].send_keys("Qq112233")
                    time.sleep(1)
            else:
                # 手机号登录流程
                logger.info(f"豆包AI-{self.worker_id} 开始手机号登录流程")

                # 输入手机号
                phone_input = self.driver.find_elements(By.XPATH, "//input[@type='tel'] | //input[@type='text'] | //input[@type='phone']")
                if phone_input:
                    # 提示用户输入手机号
                    phone_number = self._wait_for_user_input("请输入手机号码:")
                    if phone_number:
                        phone_input[0].clear()
                        phone_input[0].send_keys(phone_number)
                        time.sleep(1)

                        # 点击发送验证码
                        send_code_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '发送') or contains(text(), '获取') or contains(text(), 'Send')]")
                        if send_code_buttons:
                            send_code_buttons[0].click()
                            time.sleep(2)

                            # 等待用户输入验证码
                            verification_code = self._wait_for_user_input("请输入手机验证码:")
                            if verification_code:
                                code_input = self.driver.find_elements(By.XPATH, "//input[@type='text'] | //input[@type='number']")
                                if code_input:
                                    # 找到验证码输入框（通常是最后一个）
                                    code_input[-1].clear()
                                    code_input[-1].send_keys(verification_code)
                                    time.sleep(1)

                # 点击下一步
                next_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Next') or contains(text(), '下一步') or @id='passwordNext']")
                if next_buttons:
                    next_buttons[0].click()
                    time.sleep(5)  # 等待登录完成
                    logger.info(f"豆包AI-{self.worker_id} 密码输入完成，等待登录")

            # 等待登录完成
            time.sleep(3)
            logger.info(f"豆包AI-{self.worker_id} Google账号登录完成")

        except Exception as e:
            logger.warning(f"豆包AI-{self.worker_id} Google自动登录出错: {str(e)}")
            # 登录失败不影响后续处理，可能已经登录了

    def _process_images(self):
        """处理所有图片 - 单浏览器滚动多网页模式"""
        total_images = len(self.image_files)
        max_concurrent_tabs = 4  # 最多同时打开4个标签页

        # 首次访问豆包AI主页
        try:
            self.driver.get("https://www.doubao.com/")
            time.sleep(3)
            logger.info(f"豆包AI-{self.worker_id} 已访问主页")
        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 访问主页失败: {str(e)}")
            return

        # 真正的滚动处理模式：关闭一个页面的同时打开一个新页面
        active_tabs = []  # 存储活跃的标签页信息 [(handle, image_path, index, file_name, start_time), ...]
        next_image_index = 0  # 下一个要处理的图片索引

        logger.info(f"豆包AI-{self.worker_id} 开始滚动处理模式，最多同时处理 {max_concurrent_tabs} 张图片")

        # 第一阶段：启动初始批次
        while len(active_tabs) < max_concurrent_tabs and next_image_index < total_images and self.is_running:
            image_path = self.image_files[next_image_index]
            file_name = os.path.basename(image_path)

            try:
                # 打开新标签页
                self.driver.execute_script("window.open('https://www.doubao.com/', '_blank');")
                time.sleep(1)

                # 获取新标签页句柄
                all_handles = self.driver.window_handles
                new_handle = all_handles[-1]

                # 切换到新标签页并开始处理
                self.driver.switch_to.window(new_handle)
                time.sleep(2)

                # 开始上传图片
                self._start_image_upload(image_path, next_image_index, total_images)

                # 记录标签页信息
                import time as time_module
                active_tabs.append((new_handle, image_path, next_image_index, file_name, time_module.time()))

                logger.info(f"豆包AI-{self.worker_id} 启动处理: {file_name} ({next_image_index+1}/{total_images})")
                next_image_index += 1

            except Exception as e:
                logger.error(f"启动图片 {image_path} 处理失败: {str(e)}")
                next_image_index += 1

        # 第二阶段：滚动处理 - 检查完成的标签页，关闭并启动新的
        while active_tabs and self.is_running:
            time.sleep(5)  # 每5秒检查一次

            # 检查每个活跃标签页
            completed_tabs = []
            for i, (tab_handle, image_path, index, file_name, start_time) in enumerate(active_tabs):
                # 检查是否已经处理足够长时间（25秒）
                current_time = time.time()
                if current_time - start_time >= 25:
                    completed_tabs.append(i)

            # 处理完成的标签页（从后往前处理，避免索引问题）
            for i in reversed(completed_tabs):
                tab_handle, image_path, index, file_name, start_time = active_tabs[i]

                try:
                    # 检查窗口句柄是否仍然有效
                    current_handles = self.driver.window_handles
                    if tab_handle not in current_handles:
                        logger.warning(f"豆包AI-{self.worker_id} 标签页已关闭: {file_name}")
                        active_tabs.pop(i)
                        continue

                    # 切换到标签页检查结果
                    self.driver.switch_to.window(tab_handle)
                    time.sleep(1)

                    # 查找处理结果
                    result_url = self._find_result_image()

                    if result_url:
                        # 下载处理后的图片
                        save_path = self._download_image(result_url, image_path, self.output_folder)
                        if save_path:
                            self.processed_images.append(save_path)

                            # 根据是否启用去背景决定后续处理
                            if hasattr(self, 'enable_bg_removal') and self.enable_bg_removal and hasattr(self, 'removal_queue'):
                                # 添加到去背景队列
                                self.removal_queue.put((save_path, image_path))
                                logger.info(f"豆包AI-{self.worker_id} 图片 {file_name} 已添加到去背景队列")
                            else:
                                # 直接添加到完成队列
                                if hasattr(self, 'completed_queue'):
                                    self.completed_queue.put((save_path, True))
                                    logger.info(f"豆包AI-{self.worker_id} 图片 {file_name} 已添加到完成队列")

                        progress = int(((index + 1) / total_images) * 100)
                        status = f"已完成: {file_name}"
                        self.progress_updated.emit(progress, status, f"第 {index + 1}/{total_images} 个")

                        # 移除跨线程的回调调用以避免UI线程冲突
                        # if hasattr(self, 'progress_callback') and self.progress_callback:
                        #     try:
                        #         self.progress_callback(progress, status)
                        #     except Exception as e:
                        #         logger.error(f"回调函数调用出错: {str(e)}")
                    else:
                        # 处理失败，也要添加到完成队列（标记为失败）
                        if hasattr(self, 'completed_queue'):
                            self.completed_queue.put((image_path, False))
                            logger.info(f"豆包AI-{self.worker_id} 图片 {file_name} 处理失败，已添加到完成队列")
                        self.progress_updated.emit(int(((index + 1) / total_images) * 100), f"处理失败: {file_name}", f"第 {index + 1}/{total_images} 个")

                    # 如果还有图片要处理，先打开新标签页，然后再关闭当前标签页
                    new_handle = None
                    if next_image_index < total_images:
                        new_image_path = self.image_files[next_image_index]
                        new_file_name = os.path.basename(new_image_path)

                        try:
                            # 先打开新标签页
                            self.driver.execute_script("window.open('https://www.doubao.com/', '_blank');")
                            time.sleep(1)

                            # 获取新标签页句柄
                            all_handles = self.driver.window_handles
                            new_handle = all_handles[-1]

                            # 切换到新标签页并开始处理
                            self.driver.switch_to.window(new_handle)
                            time.sleep(2)

                            # 开始上传图片
                            self._start_image_upload(new_image_path, next_image_index, total_images)

                            logger.info(f"豆包AI-{self.worker_id} 启动新处理: {new_file_name} ({next_image_index+1}/{total_images})")

                        except Exception as e:
                            logger.error(f"启动新图片 {new_image_path} 处理失败: {str(e)}")
                            new_handle = None

                    # 现在关闭原来的标签页
                    try:
                        if tab_handle in self.driver.window_handles:
                            self.driver.switch_to.window(tab_handle)
                            self.driver.close()
                            logger.info(f"豆包AI-{self.worker_id} 已关闭: {file_name}")
                    except Exception as close_error:
                        logger.warning(f"关闭标签页时出错: {str(close_error)}")

                    # 从活跃列表中移除旧的
                    active_tabs.pop(i)

                    # 如果新标签页创建成功，添加到活跃列表
                    if new_handle and next_image_index < total_images:
                        new_image_path = self.image_files[next_image_index]
                        new_file_name = os.path.basename(new_image_path)
                        import time as time_module
                        active_tabs.append((new_handle, new_image_path, next_image_index, new_file_name, time_module.time()))
                        next_image_index += 1

                except Exception as e:
                    logger.error(f"处理图片 {image_path} 时出错: {str(e)}")
                    # 出错时也要移除并尝试关闭标签页
                    try:
                        if tab_handle in self.driver.window_handles:
                            self.driver.close()
                    except:
                        pass
                    active_tabs.pop(i)

                    # 处理出错，也要添加到完成队列（标记为失败）
                    if hasattr(self, 'completed_queue'):
                        self.completed_queue.put((image_path, False))
                        logger.info(f"豆包AI-{self.worker_id} 图片 {file_name} 处理出错，已添加到完成队列")

                    self.progress_updated.emit(int(((index + 1) / total_images) * 100), f"处理出错: {file_name}", f"第 {index + 1}/{total_images} 个")

        # 切换回主页
        try:
            remaining_handles = self.driver.window_handles
            if remaining_handles:
                # 切换到第一个窗口（通常是主页）
                self.driver.switch_to.window(remaining_handles[0])
                logger.info(f"豆包AI-{self.worker_id} 所有图片处理完成，已切换回主页")
            else:
                # 如果没有剩余窗口，重新打开主页
                self.driver.get("https://www.doubao.com/")
                logger.info(f"豆包AI-{self.worker_id} 重新打开主页")
        except Exception as e:
            logger.error(f"切换回主页失败: {str(e)}")
            # 尝试重新打开主页
            try:
                self.driver.get("https://www.doubao.com/")
                logger.info(f"豆包AI-{self.worker_id} 重新打开主页成功")
            except Exception as e2:
                logger.error(f"重新打开主页也失败: {str(e2)}")

        logger.info(f"豆包AI-{self.worker_id} 所有图片处理完成")



    def _start_image_upload(self, image_path, index, total_images):
        """在当前标签页开始图片上传和处理（不等待完成）"""
        try:
            file_name = os.path.basename(image_path)
            logger.info(f"豆包AI-{self.worker_id} 开始上传图片: {file_name}")

            # 等待页面加载完成
            WebDriverWait(self.driver, 15).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            time.sleep(2)

            # 查找文件上传输入框
            file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")
            if not file_inputs:
                # 尝试查找其他可能的上传元素
                upload_elements = self.driver.find_elements(By.XPATH, "//*[contains(@class, 'upload') or contains(@class, 'file')]")
                if upload_elements:
                    upload_elements[0].click()
                    time.sleep(1)
                    file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")

            if file_inputs:
                # 上传图片
                file_inputs[0].send_keys(image_path)
                time.sleep(3)  # 等待上传完成

                # 发送处理指令
                if self.process_type == "remove_pointer":
                    message = "帮我去掉这张图片中的指针"
                else:
                    message = "帮我去掉这张图片中的遮挡物"

                # 查找输入框并发送消息
                input_selectors = [
                    "//textarea[contains(@placeholder, '输入') or contains(@placeholder, '发送')]",
                    "//input[contains(@placeholder, '输入') or contains(@placeholder, '发送')]",
                    "//div[contains(@contenteditable, 'true')]",
                    "//textarea",
                    "//input[@type='text']"
                ]

                message_sent = False
                for selector in input_selectors:
                    try:
                        input_element = self.driver.find_element(By.XPATH, selector)
                        input_element.clear()
                        input_element.send_keys(message)

                        # 查找发送按钮
                        send_buttons = self.driver.find_elements(By.XPATH,
                            "//button[contains(text(), '发送') or contains(@class, 'send') or contains(@aria-label, '发送')]")
                        if send_buttons:
                            send_buttons[0].click()
                            message_sent = True
                            break
                        else:
                            # 尝试按回车键
                            from selenium.webdriver.common.keys import Keys
                            input_element.send_keys(Keys.RETURN)
                            message_sent = True
                            break
                    except:
                        continue

                if message_sent:
                    logger.info(f"豆包AI-{self.worker_id} 已发送处理指令: {file_name}")
                    self.progress_updated.emit(int((index / total_images) * 100), f"豆包AI-{self.worker_id} 处理中: {file_name}", f"第 {index + 1}/{total_images} 个")
                else:
                    logger.error(f"豆包AI-{self.worker_id} 无法发送处理指令: {file_name}")
            else:
                logger.error(f"豆包AI-{self.worker_id} 找不到文件上传元素: {file_name}")

        except Exception as e:
            logger.error(f"豆包AI-{self.worker_id} 上传图片失败: {str(e)}")

    def _find_result_image(self):
        """查找处理结果图片"""
        try:
            # 等待处理完成
            time.sleep(5)

            # 查找结果图片的逻辑（从原来的_process_single_image方法中提取）
            result_selectors = ["//img[contains(@src, 'http')]"]

            for selector in result_selectors:
                try:
                    images = self.driver.find_elements(By.XPATH, selector)

                    if len(images) >= 2:  # 至少要有2张图片（原图+结果图）
                        # 更严格的图片筛选
                        candidate_images = []

                        for i, img in enumerate(images):
                            result_url = img.get_attribute("src")
                            if result_url and result_url.startswith("http"):
                                # 更严格的排除条件
                                exclude_keywords = ["avatar", "user", "profile", "icon", "head", "face"]
                                exclude_patterns = ["40x40", "50x50", "60x60", "80x80", "100x100"]

                                # 检查URL是否包含排除关键词
                                url_excluded = any(keyword in result_url.lower() for keyword in exclude_keywords)
                                pattern_excluded = any(pattern in result_url for pattern in exclude_patterns)

                                if not url_excluded and not pattern_excluded:
                                    try:
                                        # 获取图片尺寸
                                        width = int(img.get_attribute("naturalWidth") or img.get_attribute("width") or "0")
                                        height = int(img.get_attribute("naturalHeight") or img.get_attribute("height") or "0")

                                        # 排除小尺寸图片（通常是头像）
                                        if width > 150 and height > 150:  # 至少150x150
                                            candidate_images.append({
                                                'url': result_url,
                                                'width': width,
                                                'height': height,
                                                'size': width * height,
                                                'index': i
                                            })
                                    except:
                                        # 如果无法获取尺寸，但URL很长，也考虑
                                        if len(result_url) > 80:
                                            candidate_images.append({
                                                'url': result_url,
                                                'width': 0,
                                                'height': 0,
                                                'size': 0,
                                                'index': i
                                            })

                        if candidate_images:
                            # 选择最大的图片
                            best_candidate = max(candidate_images, key=lambda x: x['size'])
                            return best_candidate['url']
                except Exception as e:
                    logger.error(f"查找图片出错: {str(e)}")
                    continue

            logger.error("未找到处理结果图片")
            return None

        except Exception as e:
            logger.error(f"查找结果图片时发生错误: {str(e)}")
            return None

    def _process_single_image(self, image_path):
        """处理单个图片并返回结果URL"""
        try:
            logger.info(f"豆包AI-{self.worker_id} 开始处理图片: {os.path.basename(image_path)}")

            # 等待页面加载完成
            try:
                WebDriverWait(self.driver, 15).until(
                    lambda driver: driver.execute_script("return document.readyState") == "complete"
                )
                time.sleep(3)  # 等待页面完全稳定

                # 查找文件上传输入框（可能是隐藏的）
                file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")
                if not file_inputs:
                    # 尝试查找其他可能的上传元素
                    upload_elements = self.driver.find_elements(By.XPATH, "//*[contains(@class, 'upload') or contains(@class, 'file')]")
                    if upload_elements:
                        # 点击上传区域可能会显示文件选择器
                        upload_elements[0].click()
                        time.sleep(1)
                        file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")

                if file_inputs:
                    # 上传图片
                    file_inputs[0].send_keys(image_path)
                    time.sleep(5)  # 上传后等待5秒

                    # 发送处理指令
                    if self.process_type == "remove_pointer":
                        message = "帮我去掉这张图片中的指针"
                    else:
                        message = "帮我去掉这张图片中的遮挡物"

                    # 查找输入框并发送消息
                    input_selectors = [
                        "//textarea[contains(@placeholder, '输入') or contains(@placeholder, '发送')]",
                        "//input[contains(@placeholder, '输入') or contains(@placeholder, '发送')]",
                        "//div[contains(@contenteditable, 'true')]",
                        "//textarea",
                        "//input[@type='text']"
                    ]

                    message_sent = False
                    for selector in input_selectors:
                        try:
                            input_element = self.driver.find_element(By.XPATH, selector)
                            input_element.clear()
                            input_element.send_keys(message)

                            # 查找发送按钮
                            send_buttons = self.driver.find_elements(By.XPATH,
                                "//button[contains(text(), '发送') or contains(@class, 'send') or contains(@aria-label, '发送')]")
                            if send_buttons:
                                send_buttons[0].click()
                                message_sent = True
                                break
                            else:
                                # 尝试按回车键
                                from selenium.webdriver.common.keys import Keys
                                input_element.send_keys(Keys.RETURN)
                                message_sent = True
                                break
                        except:
                            continue

                    if not message_sent:
                        logger.error("无法发送处理指令")
                        return None

                    logger.info(f"豆包AI-{self.worker_id} 指令已发送，等待AI处理...")

                    # 等待豆包AI处理完成，查找结果图片
                    time.sleep(28)  # 等待豆包AI处理图片，给AI 28秒的处理时间（原15秒+额外5秒+再加3秒）

                    # 查找豆包AI生成的结果图片
                    # 等待页面更新，确保AI生成的图片已经加载
                    time.sleep(3)

                    # 改进的图片选择逻辑，更精确地识别豆包AI生成的结果图片
                    logger.info("开始查找豆包AI生成的结果图片...")

                    # 等待页面完全加载
                    time.sleep(5)

                    # 获取页面上传前的所有图片，用于对比
                    initial_images = set()
                    try:
                        all_imgs = self.driver.find_elements(By.TAG_NAME, "img")
                        for img in all_imgs:
                            src = img.get_attribute("src")
                            if src:
                                initial_images.add(src)
                    except:
                        pass

                    # 多种策略查找结果图片
                    result_url = None

                    # 策略1: 查找最新的非头像图片
                    try:
                        # 更严格的选择器，排除头像、图标等
                        strict_selectors = [
                            # 查找聊天消息中的大图片（通常是生成结果）
                            "//div[contains(@class, 'message') or contains(@class, 'chat') or contains(@class, 'content')]//img[contains(@src, 'http') and not(contains(@src, 'avatar')) and not(contains(@src, 'user')) and not(contains(@src, 'icon')) and not(contains(@src, 'logo'))]",
                            # 查找具有特定尺寸的图片（结果图片通常较大）
                            "//img[contains(@src, 'http') and not(contains(@src, 'avatar')) and not(contains(@src, 'user')) and not(contains(@src, 'icon')) and not(contains(@src, 'logo')) and not(contains(@class, 'avatar'))]"
                        ]

                        for selector in strict_selectors:
                            images = self.driver.find_elements(By.XPATH, selector)
                            if images:
                                # 从最新的图片开始检查
                                for img in reversed(images):
                                    try:
                                        src = img.get_attribute("src")

                                        if src and src.startswith("http"):
                                            # 排除明显的头像和小图标
                                            if any(keyword in src.lower() for keyword in ['avatar', 'user', 'icon', 'logo', 'profile']):
                                                logger.debug(f"跳过头像图片(URL关键词): {src}")
                                                continue

                                            # 检查图片的实际显示尺寸
                                            try:
                                                # 获取图片的实际渲染尺寸
                                                img_rect = img.rect
                                                display_width = img_rect['width']
                                                display_height = img_rect['height']

                                                # 豆包AI头像是固定的192x192像素
                                                # 精确匹配这个尺寸来排除头像
                                                if display_width == 192 and display_height == 192:
                                                    logger.debug(f"跳过豆包AI头像(192x192): {src}")
                                                    continue

                                                # 也排除接近192x192的尺寸（考虑缩放）
                                                if 180 <= display_width <= 200 and 180 <= display_height <= 200:
                                                    aspect_ratio = display_width / display_height
                                                    if 0.9 <= aspect_ratio <= 1.1:  # 接近正方形
                                                        logger.debug(f"跳过疑似头像(接近192x192): {src} ({display_width}x{display_height})")
                                                        continue

                                                # 排除其他小的正方形图片（可能是其他头像或图标）
                                                if display_width > 0 and display_height > 0:
                                                    aspect_ratio = display_width / display_height
                                                    # 小于150x150的正方形图片很可能是头像或图标
                                                    if 0.8 <= aspect_ratio <= 1.2 and display_width <= 150 and display_height <= 150:
                                                        logger.debug(f"跳过小正方形图片: {src} ({display_width}x{display_height})")
                                                        continue

                                                logger.info(f"候选图片: {src} (尺寸: {display_width}x{display_height})")

                                            except Exception as e:
                                                logger.debug(f"无法获取图片尺寸: {str(e)}")
                                                # 如果无法获取尺寸，使用其他方法判断
                                                pass

                                            # 检查图片的CSS类名和父元素
                                            try:
                                                img_class = img.get_attribute("class") or ""
                                                parent_class = img.find_element(By.XPATH, "..").get_attribute("class") or ""

                                                # 排除明显的头像类名
                                                if any(keyword in (img_class + parent_class).lower() for keyword in ['avatar', 'user', 'profile', 'head']):
                                                    logger.debug(f"跳过头像图片(CSS类名): {src}")
                                                    continue
                                            except:
                                                pass

                                            # 检查是否是新生成的图片（不在初始图片集合中）
                                            if src not in initial_images:
                                                logger.info(f"找到新生成的图片: {src}")
                                                result_url = src
                                                break

                                            # 如果没有新图片，选择最大的非头像图片
                                            if not result_url:
                                                result_url = src
                                                logger.info(f"选择候选图片: {src}")

                                    except Exception as e:
                                        logger.debug(f"检查图片时出错: {str(e)}")
                                        continue

                                if result_url:
                                    break

                    except Exception as e:
                        logger.error(f"查找结果图片时出错: {str(e)}")

                    if result_url:
                        logger.info(f"成功找到豆包AI生成的图片: {result_url}")
                        return result_url

                    logger.error("未找到处理结果图片")
                    return None

                else:
                    logger.error("未找到文件上传输入框")
                    return None

            except TimeoutException:
                logger.error("页面加载超时")
                return None

        except Exception as e:
            logger.error(f"处理图片时发生未知错误: {str(e)}")
            return None
    
    def _download_image(self, url, original_path, output_folder=None):
        """下载处理后的图片，然后进行Removal.AI处理和裁剪"""
        try:
            # 获取文件名
            file_name = os.path.basename(original_path)
            base_name, _ = os.path.splitext(file_name)

            # 确定保存目录
            if output_folder:
                save_dir = output_folder
            else:
                # 默认保存到原图片目录下的processed文件夹
                original_dir = os.path.dirname(original_path)
                save_dir = os.path.join(original_dir, "processed")

            # 创建保存目录（如果不存在）
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            # 临时保存路径
            temp_path = os.path.join(save_dir, f"{base_name}_temp.png")

            # 下载豆包AI处理后的图片
            urllib.request.urlretrieve(url, temp_path)

            # 验证下载的图片是否是头像
            file_size = os.path.getsize(temp_path)

            # 进一步检查图片尺寸（如果可能）
            try:
                from PIL import Image
                with Image.open(temp_path) as img:
                    width, height = img.size

                    # 豆包AI头像是固定的192x192像素
                    if width == 192 and height == 192:
                        logger.warning(f"下载的是豆包AI头像(192x192)，跳过处理")
                        os.remove(temp_path)
                        return None

                    # 也检查接近192x192的尺寸
                    if 180 <= width <= 200 and 180 <= height <= 200:
                        aspect_ratio = width / height
                        if 0.9 <= aspect_ratio <= 1.1:
                            logger.warning(f"下载的疑似头像({width}x{height})，跳过处理")
                            os.remove(temp_path)
                            return None

                    # 排除其他小的正方形图片
                    if width <= 150 and height <= 150:
                        aspect_ratio = width / height
                        if 0.8 <= aspect_ratio <= 1.2:
                            logger.warning(f"下载的图片尺寸过小({width}x{height})，可能是头像，跳过处理")
                            os.remove(temp_path)
                            return None

                    logger.info(f"图片尺寸验证通过: {width}x{height}")

            except ImportError:
                logger.debug("PIL未安装，跳过图片尺寸检查")
                # 如果没有PIL，使用文件大小作为备用检查
                if file_size < 30000:  # 小于30KB的图片很可能是头像
                    logger.warning(f"下载的图片文件过小({file_size}字节)，可能是头像，跳过处理")
                    os.remove(temp_path)
                    return None
            except Exception as e:
                logger.debug(f"图片尺寸检查失败: {str(e)}")
                # 检查失败时使用文件大小作为备用
                if file_size < 30000:
                    logger.warning(f"下载的图片文件过小({file_size}字节)，可能是头像，跳过处理")
                    os.remove(temp_path)
                    return None

            logger.info(f"豆包AI处理结果已下载: {os.path.basename(temp_path)} (大小: {file_size}字节)")

            # 只有启用去背景时才进行后处理
            if self.enable_bg_removal:
                logger.info(f"开始Removal.AI去背景处理: {file_name}")
                # 使用Removal.AI去背景
                removal_success = self._removal_ai_process(temp_path)

                if removal_success:
                    # 裁剪透明区域
                    crop_success = self._crop_transparent_areas(temp_path)

                    if crop_success:
                        # 重命名为原始文件名
                        final_path = os.path.join(save_dir, f"{base_name}.png")

                        # 如果目标文件已存在，添加序号
                        counter = 1
                        while os.path.exists(final_path):
                            final_path = os.path.join(save_dir, f"{base_name}_{counter}.png")
                            counter += 1

                        os.rename(temp_path, final_path)
                        logger.info(f"完整处理完成: {os.path.basename(final_path)}")
                        return final_path
                    else:
                        logger.warning(f"裁剪失败，保留Removal.AI结果: {temp_path}")
                        return temp_path
                else:
                    logger.warning(f"Removal.AI处理失败，保留豆包AI结果: {temp_path}")
                    return temp_path
            else:
                # 未启用去背景，直接重命名为最终文件名
                final_path = os.path.join(save_dir, f"{base_name}.png")

                # 如果目标文件已存在，添加序号
                counter = 1
                while os.path.exists(final_path):
                    final_path = os.path.join(save_dir, f"{base_name}_{counter}.png")
                    counter += 1

                os.rename(temp_path, final_path)
                logger.info(f"豆包AI处理完成: {os.path.basename(final_path)}")
                return final_path

        except Exception as e:
            logger.error(f"下载和处理图片时出错: {str(e)}")
            return None



    def _removal_ai_process(self, image_path):
        """使用Removal.AI去背景"""
        try:
            # 创建浏览器用于Removal.AI
            options = Options()
            # 去背景使用无头模式
            options.add_argument('--headless=new')  # 使用无头模式
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-software-rasterizer')
            options.add_argument('--window-size=1280,800')  # 设置足够大的窗口大小以确保功能正常

            service = Service(ChromeDriverManager().install())
            removal_driver = webdriver.Chrome(service=service, options=options)

            try:
                # 打开Removal.AI网站
                removal_driver.get("https://removal.ai/")
                time.sleep(5)

                # 查找上传按钮
                upload_input = WebDriverWait(removal_driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
                )

                # 上传文件
                upload_input.send_keys(image_path)
                logger.info(f"图片已上传到Removal.AI: {os.path.basename(image_path)}")

                # 等待处理完成
                time.sleep(30)  # 等待30秒处理

                # 查找下载按钮
                download_selectors = [
                    "//a[contains(text(), 'Download')]",
                    "//button[contains(text(), 'Download')]"
                ]

                download_btn = None
                for selector in download_selectors:
                    try:
                        download_btn = WebDriverWait(removal_driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        break
                    except:
                        continue

                if download_btn:
                    # 获取下载链接
                    download_url = download_btn.get_attribute('href')
                    if download_url:
                        # 使用requests下载
                        response = requests.get(download_url, timeout=30)
                        if response.status_code == 200:
                            with open(image_path, 'wb') as f:
                                f.write(response.content)
                            logger.info(f"Removal.AI处理完成: {os.path.basename(image_path)}")
                            return True
                        else:
                            logger.error(f"下载失败: HTTP {response.status_code}")
                            return False
                    else:
                        logger.error("无法获取下载链接")
                        return False
                else:
                    logger.error("找不到下载按钮")
                    return False

            finally:
                removal_driver.quit()

        except Exception as e:
            logger.error(f"Removal.AI处理出错: {str(e)}")
            return False

    def _crop_transparent_areas(self, image_path):
        """裁剪掉透明区域，只保留有内容的部分"""
        try:
            # 打开图片
            img = Image.open(image_path).convert("RGBA")

            # 转换为numpy数组
            img_array = np.array(img)

            # 获取alpha通道
            alpha = img_array[:, :, 3]

            # 找到非透明像素的边界
            non_transparent = np.where(alpha > 0)

            if len(non_transparent[0]) == 0:
                # 如果图片完全透明，返回False
                logger.warning(f"图片完全透明，跳过裁剪: {image_path}")
                return False

            # 计算边界框
            top = non_transparent[0].min()
            bottom = non_transparent[0].max()
            left = non_transparent[1].min()
            right = non_transparent[1].max()

            # 裁剪图片
            cropped_img = img.crop((left, top, right + 1, bottom + 1))

            # 保存裁剪后的图片
            cropped_img.save(image_path, "PNG")

            original_size = f"{img.width}x{img.height}"
            cropped_size = f"{cropped_img.width}x{cropped_img.height}"
            logger.info(f"裁剪完成: {original_size} → {cropped_size}")

            return True

        except Exception as e:
            logger.error(f"裁剪失败: {str(e)}")
            return False

    def stop(self):
        """停止处理"""
        self.is_running = False

        # 安全关闭线程池
        try:
            self.doubao_executor.shutdown(wait=False)
            self.removal_executor.shutdown(wait=False)
            self.crop_executor.shutdown(wait=False)
        except Exception as e:
            logger.error(f"关闭线程池出错: {str(e)}")


class DoubaoImageProcessor(QMainWindow):
    """豆包AI图片处理主界面"""

    # 添加进度更新信号
    progress_update_signal = pyqtSignal(int, int)

    def __init__(self):
        super().__init__()
        self.image_files = []
        self.input_folder = ""
        self.output_folder = ""
        self.worker = None
        self.processed_count = 0
        self.success_count = 0

        # 登录状态
        self.login_status = False
        self.saved_login_data = self.load_login_data()

        # 连接进度更新信号
        self.progress_update_signal.connect(self.update_progress_safe)

        # 创建定时器用于检查进度
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.check_progress)
        self.completed_count = 0

        self.init_ui()

    def load_login_data(self):
        """加载保存的登录数据"""
        try:
            login_file = os.path.join(os.path.dirname(__file__), "doubao_login.json")
            if os.path.exists(login_file):
                with open(login_file, 'r', encoding='utf-8') as f:
                    import json
                    return json.load(f)
        except Exception as e:
            logger.debug(f"加载登录数据失败: {str(e)}")
        return {}

    def save_login_data(self, phone, login_success=False, cookies=None, local_storage=None):
        """保存登录数据，包括Cookie和LocalStorage"""
        try:
            login_file = os.path.join(os.path.dirname(__file__), "doubao_login.json")
            data = {
                "phone": phone,
                "login_success": login_success,
                "last_login": time.time(),
                "cookies": cookies or [],
                "local_storage": local_storage or {}
            }
            with open(login_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info("登录数据已保存（包含Cookie和LocalStorage）")
        except Exception as e:
            logger.error(f"保存登录数据失败: {str(e)}")

    def export_browser_data(self, driver):
        """导出浏览器的Cookie和LocalStorage"""
        try:
            # 获取所有Cookie
            cookies = driver.get_cookies()
            logger.info(f"导出了 {len(cookies)} 个Cookie")

            # 获取LocalStorage
            local_storage = {}
            try:
                # 执行JavaScript获取LocalStorage
                local_storage_script = """
                var storage = {};
                for (var i = 0; i < localStorage.length; i++) {
                    var key = localStorage.key(i);
                    storage[key] = localStorage.getItem(key);
                }
                return storage;
                """
                local_storage = driver.execute_script(local_storage_script)
                logger.info(f"导出了 {len(local_storage)} 个LocalStorage项")
            except Exception as e:
                logger.warning(f"导出LocalStorage失败: {str(e)}")

            # 获取SessionStorage
            session_storage = {}
            try:
                session_storage_script = """
                var storage = {};
                for (var i = 0; i < sessionStorage.length; i++) {
                    var key = sessionStorage.key(i);
                    storage[key] = sessionStorage.getItem(key);
                }
                return storage;
                """
                session_storage = driver.execute_script(session_storage_script)
                logger.info(f"导出了 {len(session_storage)} 个SessionStorage项")
                # 将SessionStorage合并到LocalStorage中
                local_storage.update(session_storage)
            except Exception as e:
                logger.warning(f"导出SessionStorage失败: {str(e)}")

            return cookies, local_storage

        except Exception as e:
            logger.error(f"导出浏览器数据失败: {str(e)}")
            return [], {}

    def load_browser_data(self, driver):
        """加载Cookie和LocalStorage到浏览器"""
        try:
            if not self.saved_login_data.get("login_success"):
                return False

            cookies = self.saved_login_data.get("cookies", [])
            local_storage = self.saved_login_data.get("local_storage", {})

            if not cookies and not local_storage:
                logger.info("没有保存的浏览器数据")
                return False

            # 先访问豆包AI主页以设置正确的域
            driver.get("https://www.doubao.com/")
            time.sleep(2)

            # 加载Cookie
            if cookies:
                for cookie in cookies:
                    try:
                        # 清理Cookie数据，移除可能导致问题的字段
                        clean_cookie = {
                            'name': cookie['name'],
                            'value': cookie['value'],
                            'domain': cookie.get('domain', '.doubao.com'),
                            'path': cookie.get('path', '/'),
                        }

                        # 只添加必要的可选字段
                        if 'secure' in cookie:
                            clean_cookie['secure'] = cookie['secure']
                        if 'httpOnly' in cookie:
                            clean_cookie['httpOnly'] = cookie['httpOnly']

                        driver.add_cookie(clean_cookie)
                    except Exception as e:
                        logger.debug(f"加载Cookie失败: {cookie.get('name', 'unknown')} - {str(e)}")

                logger.info(f"已加载 {len(cookies)} 个Cookie")

            # 加载LocalStorage和SessionStorage
            if local_storage:
                try:
                    # 构建JavaScript代码来设置LocalStorage
                    storage_script = ""
                    for key, value in local_storage.items():
                        # 转义特殊字符
                        safe_key = key.replace("'", "\\'").replace('"', '\\"')
                        safe_value = str(value).replace("'", "\\'").replace('"', '\\"')
                        storage_script += f"localStorage.setItem('{safe_key}', '{safe_value}');\n"
                        storage_script += f"sessionStorage.setItem('{safe_key}', '{safe_value}');\n"

                    if storage_script:
                        driver.execute_script(storage_script)
                        logger.info(f"已加载 {len(local_storage)} 个Storage项")
                except Exception as e:
                    logger.warning(f"加载LocalStorage失败: {str(e)}")

            # 刷新页面以应用所有数据
            driver.refresh()
            time.sleep(3)

            logger.info("浏览器数据加载完成")
            return True

        except Exception as e:
            logger.error(f"加载浏览器数据失败: {str(e)}")
            return False





    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("去指针工具")

        # 设置最小窗口尺寸，但支持最大化
        width = 500
        height = 300

        # 获取屏幕尺寸用于居中
        screen = QApplication.primaryScreen().geometry()
        self.setGeometry(
            (screen.width() - width) // 2,
            (screen.height() - height) // 2,
            width,
            height
        )
        # 设置最小尺寸，支持最大化
        self.setMinimumSize(500, 300)
        # 设置窗口可调整大小和最大化
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 设置现代化应用程序样式
        self.setStyleSheet("""
            QMainWindow {
                background: #f5f6fa;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
            }
            QLabel {
                color: #2f3542;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
            }
            QPushButton {
                background: #4a5568;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-height: 16px;
            }
            QPushButton:hover {
                background: #2d3748;
            }
            QPushButton:pressed {
                background: #1a202c;
            }
            QPushButton:disabled {
                background: #a0aec0;
                color: #718096;
            }
            QPushButton#dangerButton {
                background: #4a5568;
            }
            QPushButton#dangerButton:hover {
                background: #2d3748;
            }
            QPushButton#secondaryButton {
                background: #4a5568;
            }
            QPushButton#secondaryButton:hover {
                background: #2d3748;
            }
            QGroupBox {
                font-weight: 600;
                font-size: 13px;
                color: #2f3542;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 12px;
                background: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background: white;
            }
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 6px;
                background: white;
                selection-background-color: #3742fa;
                font-size: 12px;
                padding: 4px;
            }
            QListWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f1f2f6;
                border-radius: 3px;
                margin: 1px;
            }
            QListWidget::item:selected {
                background: #3742fa;
                color: white;
            }
            QListWidget::item:hover {
                background: #f1f2f6;
            }
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 6px;
                text-align: center;
                font-weight: 600;
                background: #f1f2f6;
                min-height: 20px;
            }
            QProgressBar::chunk {
                background: #3742fa;
                border-radius: 5px;
            }
            QRadioButton {
                font-size: 12px;
                color: #2f3542;
                spacing: 6px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #ddd;
                border-radius: 8px;
                background: white;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #3742fa;
                border-radius: 8px;
                background: #3742fa;
            }
            QCheckBox {
                font-size: 12px;
                color: #2f3542;
                spacing: 6px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ddd;
                border-radius: 3px;
                background: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2ed573;
                border-radius: 3px;
                background: #2ed573;
            }
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #3742fa;
                outline: none;
            }
        """)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 使用自适应布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(4)
        main_layout.setContentsMargins(4, 4, 4, 4)

        # 设置中央窗口部件的大小策略
        central_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 去掉标题区域

        # 输入输出文件夹选择区域
        io_group = QGroupBox("文件设置")
        io_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        io_layout = QVBoxLayout(io_group)
        io_layout.setSpacing(6)

        # 输入文件夹
        input_layout = QHBoxLayout()
        input_label = QLabel("输入文件夹:")
        input_label.setFixedWidth(90)  # 固定宽度确保对齐
        input_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.input_folder_edit = QLineEdit()
        self.input_folder_edit.setPlaceholderText("选择包含图片的文件夹...")
        self.input_folder_edit.setReadOnly(True)
        self.input_folder_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.select_input_btn = QPushButton("浏览")
        self.select_input_btn.setObjectName("secondaryButton")
        self.select_input_btn.setFixedWidth(70)
        self.select_input_btn.clicked.connect(self.select_input_folder)

        input_layout.addWidget(input_label)
        input_layout.addWidget(self.input_folder_edit, 1)  # 添加拉伸因子
        input_layout.addWidget(self.select_input_btn)
        io_layout.addLayout(input_layout)

        # 输出文件夹
        output_layout = QHBoxLayout()
        output_label = QLabel("输出文件夹:")
        output_label.setFixedWidth(90)  # 固定宽度确保对齐
        output_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.output_folder_edit = QLineEdit()
        self.output_folder_edit.setPlaceholderText("选择处理后图片的保存位置...")
        self.output_folder_edit.setReadOnly(True)
        self.output_folder_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.select_output_btn = QPushButton("浏览")
        self.select_output_btn.setObjectName("secondaryButton")
        self.select_output_btn.setFixedWidth(70)
        self.select_output_btn.clicked.connect(self.select_output_folder)

        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_folder_edit, 1)  # 添加拉伸因子
        output_layout.addWidget(self.select_output_btn)
        io_layout.addLayout(output_layout)

        # 移除扫描按钮，选择文件夹时自动加载所有图片

        # 去掉文件列表标签

        # 去掉文件列表显示，简化界面

        # 去掉文件统计标签

        main_layout.addWidget(io_group, 3)  # 给文件选择区域最多空间

        # 处理选项和操作控制合并到一行
        control_widget = QWidget()
        control_layout = QHBoxLayout(control_widget)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(15)

        # 处理类型选择
        self.type_group = QButtonGroup()
        self.remove_pointer_radio = QRadioButton("去指针")
        self.remove_obstruction_radio = QRadioButton("去遮挡物")
        self.remove_pointer_radio.setChecked(True)

        self.type_group.addButton(self.remove_pointer_radio, 0)
        self.type_group.addButton(self.remove_obstruction_radio, 1)

        # 后处理选项
        self.enable_background_removal = QCheckBox("去背景")
        self.enable_background_removal.setChecked(False)

        # 使用有头模式方便测试
        self.headless_mode = False



        # 操作按钮
        self.start_btn = QPushButton("开始处理")
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                padding: 8px 16px;
                min-height: 14px;
            }
        """)

        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.setObjectName("dangerButton")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                padding: 8px 16px;
                min-height: 14px;
            }
        """)

        self.login_btn = QPushButton("登录")
        self.login_btn.clicked.connect(self.show_login_dialog)
        self.login_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                padding: 8px 16px;
                min-height: 14px;
                background-color: #3742fa;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2f3542;
            }
        """)

        # 布局：处理类型选择 + 后处理选项 + 按钮
        control_layout.addWidget(self.remove_pointer_radio)
        control_layout.addWidget(self.remove_obstruction_radio)
        control_layout.addWidget(self.enable_background_removal)
        control_layout.addStretch()
        control_layout.addWidget(self.login_btn)
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)

        main_layout.addWidget(control_widget, 0)  # 控制区域不需要额外空间



        # 进度显示区域
        progress_group = QGroupBox("处理进度")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(4)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
                background: #ecf0f1;
                min-height: 25px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                border-radius: 6px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)

        # 进度信息
        progress_info_layout = QHBoxLayout()
        self.progress_label = QLabel("等待开始...")
        self.progress_label.setStyleSheet("""
            font-size: 13px;
            color: #2c3e50;
            padding: 4px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 5px;
        """)

        self.current_file_label = QLabel("")
        self.current_file_label.setStyleSheet("""
            font-size: 13px;
            color: #2c3e50;
            padding: 4px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 5px;
        """)

        progress_info_layout.addWidget(self.progress_label)
        progress_info_layout.addWidget(self.current_file_label)
        progress_info_layout.addStretch()
        progress_layout.addLayout(progress_info_layout)

        # 统计信息
        stats_layout = QHBoxLayout()
        self.processed_count_label = QLabel("已处理: 0")
        self.total_count_label = QLabel("总计: 0")
        self.success_rate_label = QLabel("成功率: 0%")

        for label in [self.processed_count_label, self.total_count_label, self.success_rate_label]:
            label.setStyleSheet("""
                font-size: 12px;
                color: #7f8c8d;
                padding: 3px 8px;
                background: #ecf0f1;
                border-radius: 3px;
                margin: 2px;
            """)

        stats_layout.addWidget(self.processed_count_label)
        stats_layout.addWidget(self.total_count_label)
        stats_layout.addWidget(self.success_rate_label)
        stats_layout.addStretch()
        progress_layout.addLayout(stats_layout)

        main_layout.addWidget(progress_group, 0)  # 进度显示区域不需要额外空间

        # 状态栏
        self.statusBar().showMessage("就绪")
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background: #2f3542;
                color: white;
                font-weight: 600;
                padding: 4px 8px;
                font-size: 12px;
            }
        """)

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 当窗口大小改变时，确保布局正确更新
        if hasattr(self, 'file_list'):
            # 根据窗口高度调整文件列表的最小高度
            window_height = self.height()
            # 在小窗口时使用更小的最小高度
            if window_height < 500:
                min_list_height = max(30, int(window_height * 0.1))
            else:
                min_list_height = max(40, int(window_height * 0.12))
            self.file_list.setMinimumHeight(min_list_height)

    def select_input_folder(self):
        """选择输入文件夹并自动加载所有图片文件"""
        folder = QFileDialog.getExistingDirectory(self, "选择输入文件夹", "")

        if folder:
            self.input_folder = folder
            self.input_folder_edit.setText(folder)

            # 如果输出文件夹为空，默认设置为输入文件夹下的processed子文件夹
            if not self.output_folder:
                self.output_folder = os.path.join(folder, "processed")
                self.output_folder_edit.setText(self.output_folder)

            # 自动扫描并加载文件夹中的所有图片
            self.auto_load_images_from_folder()

            self.statusBar().showMessage(f"🟢 已选择输入文件夹: {folder}")

    def auto_load_images_from_folder(self):
        """自动从输入文件夹加载所有图片文件"""
        if not self.input_folder:
            return

        # 支持的图片格式
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff']

        # 清空当前列表
        self.image_files.clear()

        try:
            # 遍历文件夹中的所有图片文件
            for root, dirs, files in os.walk(self.input_folder):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        self.image_files.append(os.path.join(root, file))

            # 更新文件列表
            self.update_file_list()

            # 显示结果
            if len(self.image_files) > 0:
                self.statusBar().showMessage(f"🔍 自动加载完成，找到 {len(self.image_files)} 个图片文件")
                # 启用开始处理按钮
                self.start_btn.setEnabled(True)
            else:
                self.statusBar().showMessage("⚠️ 文件夹中未找到图片文件")
                QMessageBox.information(self, "ℹ️ 提示", "在选择的文件夹中未找到支持的图片文件。\n\n支持的格式: PNG, JPG, JPEG, BMP, GIF, TIFF")
                # 禁用开始处理按钮
                self.start_btn.setEnabled(False)

        except Exception as e:
            QMessageBox.critical(self, "❌ 错误", f"加载图片文件时出错:\n\n{str(e)}")
            self.statusBar().showMessage("❌ 加载出错")

    def select_output_folder(self):
        """选择输出文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹", "")

        if folder:
            self.output_folder = folder
            self.output_folder_edit.setText(folder)
            self.statusBar().showMessage(f"🟢 已选择输出文件夹: {folder}")

    def scan_input_folder(self):
        """扫描输入文件夹中的图片"""
        if not self.input_folder:
            QMessageBox.warning(self, "⚠️ 提示", "请先选择输入文件夹！")
            # 如果没有选择输入文件夹，则打开选择对话框
            self.select_input_folder()
            if not self.input_folder:  # 如果用户取消了选择
                return

        # 支持的图片格式
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff']

        # 清空当前列表
        self.image_files.clear()

        try:
            # 显示进度对话框
            progress_dialog = QMessageBox(self)
            progress_dialog.setWindowTitle("🔍 扫描中")
            progress_dialog.setText("正在扫描输入文件夹中的图片，请稍候...")
            progress_dialog.setStandardButtons(QMessageBox.NoButton)
            progress_dialog.show()
            QApplication.processEvents()

            # 遍历文件夹中的所有图片文件
            for root, dirs, files in os.walk(self.input_folder):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        self.image_files.append(os.path.join(root, file))
                QApplication.processEvents()  # 保持UI响应

            progress_dialog.close()

            # 更新文件列表
            self.update_file_list()

            # 显示结果
            if len(self.image_files) > 0:
                self.statusBar().showMessage(f"🔍 扫描完成，找到 {len(self.image_files)} 个图片文件")
                QMessageBox.information(self, "✅ 扫描完成", f"在输入文件夹中找到 {len(self.image_files)} 个图片文件。")
            else:
                self.statusBar().showMessage("⚠️ 未找到图片文件")
                QMessageBox.warning(self, "⚠️ 扫描结果", "在输入文件夹中未找到支持的图片文件。\n\n支持的格式: PNG, JPG, JPEG, BMP, GIF, TIFF")

        except Exception as e:
            QMessageBox.critical(self, "❌ 错误", f"扫描文件夹时出错:\n\n{str(e)}")
            self.statusBar().showMessage("❌ 扫描出错")

    def select_files(self):
        """选择图片文件 - 此方法保留但不再直接使用"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择图片文件",
            self.input_folder if self.input_folder else "",
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff);;所有文件 (*)"
        )

        if files:
            self.image_files.extend(files)
            self.update_file_list()

    def clear_files(self):
        """清空文件列表 - 内部使用"""
        self.image_files.clear()
        self.update_file_list()
        self.statusBar().showMessage("🗑️ 已清空文件列表")

    def update_file_list(self):
        """更新文件统计显示"""
        self.total_count_label.setText(f"总计: {len(self.image_files)}")

        # 更新状态栏
        if len(self.image_files) > 0:
            self.statusBar().showMessage("就绪")
        else:
            self.statusBar().showMessage("文件列表为空")

    # 去掉右键菜单，因为已经移除文件列表显示
    def show_context_menu_disabled(self, position):
        """显示右键菜单"""
        if self.file_list.itemAt(position) is None:
            return

        menu = QMenu(self)

        # 添加菜单项
        add_files_action = QAction("📷 添加图片文件", self)
        add_files_action.triggered.connect(self.select_files)
        menu.addAction(add_files_action)

        menu.addSeparator()

        remove_selected_action = QAction("❌ 移除选中项", self)
        remove_selected_action.triggered.connect(self.remove_selected_files)
        menu.addAction(remove_selected_action)

        clear_all_action = QAction("🗑️ 清空所有", self)
        clear_all_action.triggered.connect(self.clear_files)
        menu.addAction(clear_all_action)

        menu.addSeparator()

        refresh_action = QAction("🔄 重新加载", self)
        refresh_action.triggered.connect(self.auto_load_images_from_folder)
        menu.addAction(refresh_action)

        # 显示菜单
        menu.exec_(self.file_list.mapToGlobal(position))

    def remove_selected_files(self):
        """移除选中的文件"""
        current_row = self.file_list.currentRow()
        if current_row >= 0 and current_row < len(self.image_files):
            file_name = os.path.basename(self.image_files[current_row])
            reply = QMessageBox.question(
                self,
                "⚠️ 确认移除",
                f"确定要从列表中移除文件 '{file_name}' 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.image_files.pop(current_row)
                self.update_file_list()
                self.statusBar().showMessage(f"🗑️ 已移除文件: {file_name}")

    def start_processing(self):
        """开始处理图片"""
        # 验证输入
        if not self.image_files:
            QMessageBox.warning(self, "⚠️ 警告", "请先选择要处理的图片文件！")
            return

        if not self.output_folder:
            QMessageBox.warning(self, "⚠️ 警告", "请先选择输出文件夹！")
            return

        # 确保输出文件夹存在
        try:
            if not os.path.exists(self.output_folder):
                os.makedirs(self.output_folder)
        except Exception as e:
            QMessageBox.critical(self, "❌ 错误", f"无法创建输出文件夹:\n{str(e)}")
            return

        # 获取处理类型
        process_type = "remove_pointer" if self.remove_pointer_radio.isChecked() else "remove_obstruction"

        # 使用正常浏览器模式（不使用无头模式）

        # 重置统计
        self.processed_count = 0
        self.success_count = 0
        self.start_time = time.time()  # 记录开始时间

        # 创建并启动并行处理器（优化并发数）
        self.parallel_processor = ParallelProcessor(max_doubao_workers=3, max_removal_workers=3)

        # 设置主窗口的completed_queue引用为ParallelProcessor的队列
        self.completed_queue = self.parallel_processor.completed_queue

        # 移除跨线程的进度回调以避免UI线程冲突
        # self.parallel_processor.main_window_progress_callback = self.update_progress_direct

        # 获取后处理选项
        enable_bg_removal = self.enable_background_removal.isChecked()

        # 启动并行处理
        self.parallel_processor.start_processing(
            self.image_files.copy(),
            process_type,
            self.output_folder,
            self.update_progress_parallel,
            enable_bg_removal
        )

        # 重置计数器
        self.completed_count = 0
        self.success_count = 0

        # 启动进度检查定时器（每500毫秒检查一次）
        self.progress_timer.start(500)
        logger.info("启动进度检查定时器")

        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("🚀 开始处理...")
        self.processed_count_label.setText("已处理: 0")
        self.statusBar().showMessage("处理中...")

    def update_progress_parallel(self, message):
        """并行处理进度更新"""
        try:
            self.statusBar().showMessage(message)
            logger.info(f"状态栏更新: {message}")
        except Exception as e:
            logger.error(f"状态栏更新出错: {str(e)}")

    def update_progress_direct(self, progress, status):
        """直接更新进度条（从worker回调）"""
        try:
            # 直接更新进度条
            self.progress_bar.setValue(progress)
            self.progress_bar.repaint()  # 强制重绘

            # 更新状态
            self.progress_label.setText(status)

            # 强制处理事件
            QApplication.processEvents()

            logger.info(f"直接进度更新: {progress}% - {status}")

        except Exception as e:
            logger.error(f"直接进度更新出错: {str(e)}")

    def update_progress_from_worker(self, progress, status):
        """从worker接收进度更新（直接更新进度条）"""
        try:
            # 直接更新进度条
            self.progress_bar.setValue(progress)
            self.progress_bar.repaint()  # 强制重绘

            # 更新状态
            self.progress_label.setText(status)

            # 强制处理事件
            QApplication.processEvents()

            logger.info(f"Worker进度更新: {progress}% - {status}")

        except Exception as e:
            logger.error(f"Worker进度更新出错: {str(e)}")

    def check_progress(self):
        """定时检查进度（在主线程中运行）"""
        try:
            if hasattr(self, 'completed_queue'):
                # 检查是否有新的完成项目
                while not self.completed_queue.empty():
                    try:
                        result = self.completed_queue.get_nowait()
                        if result:
                            file_path, success = result
                            self.completed_count += 1
                            if success:
                                self.success_count += 1

                            # 计算进度
                            progress = int((self.completed_count / len(self.image_files)) * 100)

                            # 直接更新进度条（强制更新）
                            self.progress_bar.setValue(progress)
                            self.progress_bar.repaint()  # 强制重绘

                            # 更新标签
                            self.processed_count_label.setText(f"已处理: {self.completed_count}/{len(self.image_files)}")
                            self.total_count_label.setText(f"总计: {len(self.image_files)}")

                            # 计算成功率
                            if self.completed_count > 0:
                                success_rate = (self.success_count / self.completed_count * 100)
                                self.success_rate_label.setText(f"成功率: {success_rate:.1f}%")

                            # 强制处理事件
                            QApplication.processEvents()

                            logger.info(f"定时器进度更新: {progress}% ({self.completed_count}/{len(self.image_files)})")

                            # 如果处理完成，停止定时器
                            if self.completed_count >= len(self.image_files):
                                self.progress_timer.stop()
                                logger.info("所有图片处理完成，停止进度定时器")

                    except queue.Empty:
                        break

        except Exception as e:
            logger.error(f"检查进度出错: {str(e)}")

    @pyqtSlot(int, int)
    def update_progress_safe(self, progress, completed_count):
        """安全地更新进度（在主线程中调用）"""
        try:
            self.progress_bar.setValue(progress)

            # 计算剩余时间
            if completed_count > 0 and hasattr(self, 'start_time'):
                elapsed_time = time.time() - self.start_time
                avg_time_per_image = elapsed_time / completed_count
                remaining_images = len(self.image_files) - completed_count
                remaining_time = avg_time_per_image * remaining_images

                # 格式化剩余时间
                if remaining_time > 3600:  # 超过1小时
                    hours = int(remaining_time // 3600)
                    minutes = int((remaining_time % 3600) // 60)
                    time_str = f"{hours}小时{minutes}分钟"
                elif remaining_time > 60:  # 超过1分钟
                    minutes = int(remaining_time // 60)
                    seconds = int(remaining_time % 60)
                    time_str = f"{minutes}分{seconds}秒"
                else:  # 小于1分钟
                    seconds = int(remaining_time)
                    time_str = f"{seconds}秒"

                self.processed_count_label.setText(f"已处理: {completed_count}/{len(self.image_files)} | 剩余时间: {time_str}")
            else:
                self.processed_count_label.setText(f"已处理: {completed_count}/{len(self.image_files)}")

            self.total_count_label.setText(f"总计: {len(self.image_files)}")

            # 计算成功率
            if hasattr(self, 'success_count'):
                success_rate = (self.success_count / completed_count * 100) if completed_count > 0 else 0
                self.success_rate_label.setText(f"成功率: {success_rate:.1f}%")

            logger.info(f"进度更新: {progress}% ({completed_count}/{len(self.image_files)})")
        except Exception as e:
            logger.error(f"更新进度出错: {str(e)}")

    def monitor_progress(self):
        """监控并行处理进度"""
        completed_images = []

        try:
            while True:
                try:
                    # 检查完成队列
                    if hasattr(self, 'parallel_processor') and self.parallel_processor.is_running:
                        result = self.parallel_processor.completed_queue.get(timeout=1)
                        if result:
                            file_path, _ = result  # success变量未使用
                            completed_images.append(file_path)

                            # 计算进度
                            progress = int((len(completed_images) / len(self.image_files)) * 100)
                            completed_count = len(completed_images)

                            # 使用信号更新UI（线程安全）
                            try:
                                # 发出进度更新信号
                                self.progress_update_signal.emit(progress, completed_count)
                                logger.info(f"进度更新: {progress}% ({completed_count}/{len(self.image_files)})")

                                # 同时直接调用更新方法（确保UI更新）
                                QApplication.processEvents()  # 处理待处理的事件

                            except Exception as e:
                                logger.error(f"更新UI出错: {str(e)}")

                            # 检查是否全部完成
                            if len(completed_images) >= len(self.image_files):
                                # 直接调用完成处理，避免lambda闭包问题
                                try:
                                    self.processing_complete_parallel(completed_images.copy())
                                except Exception as e:
                                    logger.error(f"完成处理调用出错: {str(e)}")
                                break
                    else:
                        break

                except queue.Empty:
                    # 检查是否应该停止
                    if not hasattr(self, 'parallel_processor') or not self.parallel_processor.is_running:
                        break
                    continue
                except Exception as e:
                    logger.error(f"监控进度出错: {str(e)}")
                    break

        except Exception as e:
            logger.error(f"监控线程出错: {str(e)}")

        logger.info("监控线程退出")

    def processing_complete_parallel(self, completed_images):
        """并行处理完成"""
        try:
            # 安全地停止并行处理器
            if hasattr(self, 'parallel_processor'):
                self.parallel_processor.stop()

            self.reset_ui_state()

            success_count = len([img for img in completed_images if img])
            total_count = len(self.image_files)
            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0

            # 更新统计显示
            self.processed_count_label.setText(f"已处理: {total_count}")
            self.success_rate_label.setText(f"成功率: {success_rate:.1f}%")

            # 显示完成消息
            if success_count > 0:
                message = f"🎉 并行处理完成！\n\n✅ 成功处理: {success_count} 张图片\n❌ 处理失败: {total_count - success_count} 张图片\n📈 成功率: {success_rate:.1f}%\n\n📁 处理后的图片保存在:\n{self.output_folder}"
                QMessageBox.information(self, "✅ 处理完成", message)
            else:
                message = f"😞 处理完成，但没有成功处理任何图片。\n\n请检查网络连接和服务状态。"
                QMessageBox.warning(self, "⚠️ 处理完成", message)

            self.statusBar().showMessage(f"✅ 并行处理完成 - 成功: {success_count}/{total_count} ({success_rate:.1f}%)")

        except Exception as e:
            logger.error(f"处理完成回调出错: {str(e)}")
            self.reset_ui_state()

    def stop_processing(self):
        """停止处理"""
        reply = QMessageBox.question(
            self,
            "⚠️ 确认停止",
            "确定要停止当前处理任务吗？\n已处理的图片将被保留。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if hasattr(self, 'parallel_processor'):
                self.parallel_processor.stop()
                self.statusBar().showMessage("⏹️ 并行处理已停止")
            elif hasattr(self, 'worker') and self.worker.isRunning():
                self.worker.stop()
                self.worker.wait()
                self.statusBar().showMessage("⏹️ 处理已停止")

            self.reset_ui_state()

    def update_progress(self, value, message, current_info=""):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        self.current_file_label.setText(current_info)

        # 更新统计信息
        if "已完成" in message:
            self.processed_count += 1
            self.success_count += 1
        elif "处理失败" in message or "处理出错" in message:
            self.processed_count += 1

        # 更新统计标签
        self.processed_count_label.setText(f"已处理: {self.processed_count}")
        if self.processed_count > 0:
            success_rate = (self.success_count / self.processed_count) * 100
            self.success_rate_label.setText(f"成功率: {success_rate:.1f}%")

    def processing_complete(self, processed_images):
        """处理完成"""
        self.reset_ui_state()

        success_count = len([img for img in processed_images if img is not None])
        total_count = len(self.image_files)
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0

        # 更新最终统计
        self.processed_count_label.setText(f"已处理: {total_count}")
        self.success_rate_label.setText(f"成功率: {success_rate:.1f}%")

        # 显示结果消息
        if success_count > 0:
            message = f"🎉 处理完成！\n\n✅ 成功处理: {success_count} 张图片\n❌ 处理失败: {total_count - success_count} 张图片\n📈 成功率: {success_rate:.1f}%\n\n📁 处理后的图片保存在:\n{self.output_folder}"
            QMessageBox.information(self, "✅ 处理完成", message)
        else:
            message = f"😞 处理完成，但没有成功处理任何图片。\n\n请检查网络连接和豆包AI服务状态。"
            QMessageBox.warning(self, "⚠️ 处理完成", message)

        self.statusBar().showMessage(f"✅ 处理完成 - 成功: {success_count}/{total_count} ({success_rate:.1f}%)")

    def handle_error(self, error_message):
        """处理错误"""
        self.reset_ui_state()
        QMessageBox.critical(self, "❌ 错误", f"处理过程中发生错误:\n\n{error_message}")
        self.statusBar().showMessage("❌ 处理出错")

    def reset_ui_state(self):
        """重置UI状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_label.setText("💤 等待开始...")

    def show_login_dialog(self):
        """显示登录对话框"""
        try:
            dialog = DoubaoLoginDialog(self)

            if dialog.exec_() == QDialog.Accepted:
                phone, code = dialog.get_login_info()
                browser_id = dialog.selected_browser

                if phone and code:
                    QMessageBox.information(self, "登录信息",
                        f"浏览器{browser_id + 1}:\n手机号: {phone}\n验证码: {code}")
                elif phone:
                    QMessageBox.information(self, "登录信息",
                        f"浏览器{browser_id + 1}:\n手机号: {phone}\n验证码已发送，请在弹窗中输入")

        except Exception as e:
            logger.error(f"显示登录对话框出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"显示登录对话框出错:\n{str(e)}")

    def closeEvent(self, event):
        """关闭事件"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self,
                "⚠️ 确认退出",
                "正在处理图片，确定要退出吗？\n当前进度将会丢失，但已处理的图片会被保留。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.worker.stop()
                self.worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()





    def auto_input_phone_number(self, phone):
        """自动在豆包AI页面输入手机号"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # 设置Chrome选项
            options = Options()
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            # 登录时使用有头模式但移到屏幕外，保证登录记忆正常
            use_headless = False  # 登录时不使用无头模式
            options.add_argument('--window-size=1280,800')  # 设置足够大的窗口大小以确保功能正常
            options.add_argument('--window-position=-2000,-2000')  # 将窗口移到屏幕外
            options.add_argument('--disable-software-rasterizer')

            # 添加用户数据目录以保持登录状态
            import tempfile
            user_data_dir = os.path.join(tempfile.gettempdir(), "doubao_chrome_profile_login")
            options.add_argument(f'--user-data-dir={user_data_dir}')

            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)

            try:
                # 访问豆包AI首页
                driver.get("https://www.doubao.com/")

                # 等待页面加载
                time.sleep(3)

                # 先点击登录按钮进入登录界面
                if not self._click_login_button(driver):
                    logger.warning("无法找到登录按钮，尝试直接访问登录页面")
                    # 尝试直接访问登录页面
                    driver.get("https://www.doubao.com/login")
                    time.sleep(3)

                # 查找手机号输入框
                phone_selectors = [
                    "//input[@placeholder='请输入手机号' or contains(@placeholder, '手机号')]",
                    "//input[@type='tel']",
                    "//input[contains(@class, 'phone')]",
                    "//input[contains(@name, 'phone')]"
                ]

                phone_input_found = False
                for selector in phone_selectors:
                    try:
                        phone_elements = driver.find_elements(By.XPATH, selector)
                        for elem in phone_elements:
                            if elem.is_displayed() and elem.is_enabled():
                                elem.clear()
                                elem.send_keys(phone)
                                logger.info(f"已自动输入手机号: {phone}")
                                phone_input_found = True
                                break
                        if phone_input_found:
                            break
                    except Exception as e:
                        logger.debug(f"尝试输入手机号失败: {str(e)}")
                        continue

                if not phone_input_found:
                    logger.warning("未找到手机号输入框，请手动输入")
                else:
                    # 尝试自动点击获取验证码按钮
                    time.sleep(2)  # 等待页面响应
                    self._auto_click_get_code_button(driver)

                # 保持浏览器打开，让用户手动操作
                self.login_driver = driver  # 保存driver引用

            except Exception as e:
                driver.quit()
                raise e

        except Exception as e:
            logger.error(f"自动输入手机号失败: {str(e)}")
            raise e

    def _auto_click_get_code_button(self, driver):
        """自动点击获取验证码按钮"""
        try:
            # 查找获取验证码按钮（包括"下一步"按钮）
            code_button_selectors = [
                "//button[contains(text(), '获取验证码') or contains(text(), '发送验证码') or contains(text(), '获取短信')]",
                "//button[contains(text(), '下一步') or contains(text(), '下一步')]",
                "//button[contains(text(), '发送') and contains(text(), '验证码')]",
                "//button[contains(@class, 'code') or contains(@class, 'sms')]",
                "//button[contains(@class, 'next') or contains(@class, 'continue')]",
                "//div[contains(text(), '获取验证码')]//parent::button",
                "//div[contains(text(), '下一步')]//parent::button",
                "//span[contains(text(), '获取验证码')]//parent::button",
                "//span[contains(text(), '下一步')]//parent::button",
                "//a[contains(text(), '获取验证码')]",
                "//a[contains(text(), '下一步')]"
            ]

            button_clicked = False
            for selector in code_button_selectors:
                try:
                    code_buttons = driver.find_elements(By.XPATH, selector)
                    for btn in code_buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            # 检查按钮文本确认是获取验证码按钮
                            btn_text = btn.text.strip()
                            if any(keyword in btn_text for keyword in ['获取验证码', '发送验证码', '获取短信', '发送短信', '下一步']):
                                btn.click()
                                logger.info(f"已自动点击获取验证码按钮: {btn_text}")
                                button_clicked = True
                                break
                    if button_clicked:
                        break
                except Exception as e:
                    logger.debug(f"尝试点击验证码按钮失败: {str(e)}")
                    continue

            if not button_clicked:
                logger.warning("未找到获取验证码按钮，请手动点击")
                # 尝试通过JavaScript查找并点击
                try:
                    js_script = """
                    var buttons = document.querySelectorAll('button, div, span, a');
                    for (var i = 0; i < buttons.length; i++) {
                        var text = buttons[i].textContent || buttons[i].innerText || '';
                        if (text.includes('获取验证码') || text.includes('发送验证码') || text.includes('获取短信') || text.includes('下一步')) {
                            if (buttons[i].offsetParent !== null) {  // 检查元素是否可见
                                buttons[i].click();
                                return '已通过JavaScript点击: ' + text;
                            }
                        }
                    }
                    return '未找到可点击的验证码按钮';
                    """
                    result = driver.execute_script(js_script)
                    logger.info(f"JavaScript点击结果: {result}")
                except Exception as e:
                    logger.debug(f"JavaScript点击验证码按钮失败: {str(e)}")

        except Exception as e:
            logger.error(f"自动点击获取验证码按钮失败: {str(e)}")

    def _click_login_button(self, driver):
        """点击登录按钮进入登录界面"""
        try:
            # 查找登录按钮
            login_button_selectors = [
                "//button[contains(text(), '登录') or contains(text(), '登陆') or contains(text(), 'Login')]",
                "//a[contains(text(), '登录') or contains(text(), '登陆') or contains(text(), 'Login')]",
                "//div[contains(text(), '登录') or contains(text(), '登陆')]//parent::button",
                "//span[contains(text(), '登录') or contains(text(), '登陆')]//parent::button",
                "//button[contains(@class, 'login')]",
                "//a[contains(@class, 'login')]",
                "//div[contains(@class, 'login') and @role='button']",
                "//button[contains(@aria-label, '登录')]"
            ]

            button_clicked = False
            for selector in login_button_selectors:
                try:
                    login_buttons = driver.find_elements(By.XPATH, selector)
                    for btn in login_buttons:
                        if btn.is_displayed() and btn.is_enabled():
                            # 检查按钮文本确认是登录按钮
                            btn_text = btn.text.strip()
                            if any(keyword in btn_text for keyword in ['登录', '登陆', 'Login', '']) or 'login' in btn.get_attribute('class').lower():
                                btn.click()
                                logger.info(f"已点击登录按钮: {btn_text}")
                                time.sleep(3)  # 等待登录页面加载
                                button_clicked = True
                                break
                    if button_clicked:
                        break
                except Exception as e:
                    logger.debug(f"尝试点击登录按钮失败: {str(e)}")
                    continue

            if not button_clicked:
                # 尝试通过JavaScript查找并点击登录按钮
                try:
                    js_script = """
                    var elements = document.querySelectorAll('button, a, div, span');
                    for (var i = 0; i < elements.length; i++) {
                        var text = elements[i].textContent || elements[i].innerText || '';
                        var className = elements[i].className || '';
                        if ((text.includes('登录') || text.includes('登陆') || text.includes('Login') ||
                             className.toLowerCase().includes('login')) &&
                            elements[i].offsetParent !== null) {
                            elements[i].click();
                            return '已通过JavaScript点击登录按钮: ' + text;
                        }
                    }
                    return '未找到登录按钮';
                    """
                    result = driver.execute_script(js_script)
                    logger.info(f"JavaScript点击登录按钮结果: {result}")
                    if '已通过JavaScript点击登录按钮' in result:
                        time.sleep(3)
                        button_clicked = True
                except Exception as e:
                    logger.debug(f"JavaScript点击登录按钮失败: {str(e)}")

            return button_clicked

        except Exception as e:
            logger.error(f"点击登录按钮失败: {str(e)}")
            return False

    def perform_login(self):
        """执行登录"""
        phone = self.phone_input.text().strip()
        code = self.code_input.text().strip()

        if not phone or not code:
            QMessageBox.warning(self, "提示", "请输入手机号和验证码")
            return

        try:
            # 尝试自动输入验证码
            if hasattr(self, 'login_driver') and self.login_driver:
                self.auto_input_verification_code(code)

                # 等待登录完成并导出数据
                QMessageBox.information(self, "提示",
                    "验证码已输入！\n"
                    "请在浏览器中确认登录，\n"
                    "登录成功后点击【确定】导出登录数据")

                # 导出Cookie和LocalStorage
                cookies, local_storage = self.export_browser_data(self.login_driver)

                # 保存完整的登录数据
                self.save_login_data(phone, True, cookies, local_storage)
                self.update_login_status(True)

                # 清空验证码
                self.code_input.clear()

                # 关闭登录浏览器
                try:
                    self.login_driver.quit()
                    self.login_driver = None
                except:
                    pass

                QMessageBox.information(self, "成功",
                    f"登录数据导出成功！\n"
                    f"Cookie: {len(cookies)} 个\n"
                    f"Storage: {len(local_storage)} 个\n"
                    "下次启动将自动免登录")
            else:
                # 没有浏览器实例，只保存基本信息
                self.save_login_data(phone, True)
                self.update_login_status(True)
                self.code_input.clear()

                QMessageBox.information(self, "提示",
                    "登录信息已保存！\n"
                    "请确保在豆包AI网页中完成登录")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"登录失败: {str(e)}")

    def auto_input_verification_code(self, code):
        """简化的验证码输入 - 提示用户手动输入"""
        try:
            logger.info(f"用户输入验证码: {code}")
            # 简化：不自动输入验证码，只提示用户手动操作
            QMessageBox.information(self, "验证码输入",
                f"验证码: {code}\n\n请在浏览器中手动输入此验证码并点击登录")
        except Exception as e:
            logger.error(f"验证码处理失败: {str(e)}")

    def perform_logout(self):
        """执行退出登录"""
        try:
            # 关闭登录浏览器
            if hasattr(self, 'login_driver') and self.login_driver:
                try:
                    self.login_driver.quit()
                    self.login_driver = None
                except:
                    pass

            # 清除保存的登录数据
            self.save_login_data("", False)
            self.update_login_status(False)

            # 清空输入框
            self.phone_input.clear()
            self.code_input.clear()

            QMessageBox.information(self, "提示", "已退出登录")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"退出登录失败: {str(e)}")


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    logger.error("未捕获的异常:", exc_info=(exc_type, exc_value, exc_traceback))
    print(f"程序出现异常: {exc_type.__name__}: {exc_value}")

def main():
    """主函数"""
    try:
        # 设置全局异常处理
        sys.excepthook = handle_exception

        app = QApplication(sys.argv)

        # 设置应用程序信息
        app.setApplicationName("图片处理工具")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("AI助手")

        # 创建并显示主窗口
        window = DoubaoImageProcessor()
        window.show()

        # 运行应用程序
        sys.exit(app.exec_())
    except Exception as e:
        logger.error(f"程序启动失败: {str(e)}")
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    main()
