@echo off
chcp 65001 >nul
echo ================================================
echo 去指针工具 - 快速打包脚本
echo ================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python并添加到PATH环境变量
    pause
    exit /b 1
)

echo 正在检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo PyInstaller未安装，正在安装...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo 清理旧的构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__
if exist *.spec del *.spec

echo 开始打包...
python -m PyInstaller ^
    --onefile ^
    --windowed ^
    --name "去指针工具" ^
    --add-data "license.dat;." ^
    --hidden-import selenium ^
    --hidden-import selenium.webdriver ^
    --hidden-import selenium.webdriver.chrome ^
    --hidden-import selenium.webdriver.chrome.options ^
    --hidden-import selenium.webdriver.chrome.service ^
    --hidden-import selenium.webdriver.common.by ^
    --hidden-import selenium.webdriver.support.ui ^
    --hidden-import selenium.webdriver.support.expected_conditions ^
    --hidden-import webdriver_manager ^
    --hidden-import webdriver_manager.chrome ^
    --hidden-import PyQt5 ^
    --hidden-import PyQt5.QtWidgets ^
    --hidden-import PyQt5.QtCore ^
    --hidden-import PyQt5.QtGui ^
    --hidden-import PIL ^
    --hidden-import PIL.Image ^
    --hidden-import numpy ^
    --hidden-import requests ^
    --hidden-import psutil ^
    --hidden-import pyperclip ^
    doubao_image_processor.py

if errorlevel 1 (
    echo 错误: 打包失败
    pause
    exit /b 1
)

echo 复制额外文件...
if exist "记忆功能说明.md" copy "记忆功能说明.md" dist\
if exist "license.dat" copy "license.dat" dist\

echo 创建使用说明...
echo # 去指针工具 - 使用说明 > "dist\使用说明.txt"
echo. >> "dist\使用说明.txt"
echo ## 使用方法 >> "dist\使用说明.txt"
echo 1. 双击运行"去指针工具.exe" >> "dist\使用说明.txt"
echo 2. 选择输入和输出文件夹 >> "dist\使用说明.txt"
echo 3. 选择处理类型（去指针或去遮挡物） >> "dist\使用说明.txt"
echo 4. 可选择是否启用去背景功能 >> "dist\使用说明.txt"
echo 5. 登录豆包AI账号 >> "dist\使用说明.txt"
echo 6. 开始处理 >> "dist\使用说明.txt"
echo. >> "dist\使用说明.txt"
echo ## 注意事项 >> "dist\使用说明.txt"
echo - 需要联网使用 >> "dist\使用说明.txt"
echo - 需要豆包AI账号 >> "dist\使用说明.txt"
echo - 用户选择会自动保存 >> "dist\使用说明.txt"

echo.
echo ================================================
echo ✓ 打包完成!
echo 可执行文件位置: dist\去指针工具.exe
echo ================================================
echo.
pause
