#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试记忆功能的简单脚本
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QRadioButton, QCheckBox, QPushButton, QLabel, QButtonGroup

class TestMemoryWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_user_preferences()

    def init_ui(self):
        self.setWindowTitle("测试记忆功能")
        self.setGeometry(300, 300, 400, 200)

        layout = QVBoxLayout()

        # 处理类型选择
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("处理类型:"))
        
        self.type_group = QButtonGroup()
        self.remove_pointer_radio = QRadioButton("去指针")
        self.remove_obstruction_radio = QRadioButton("去遮挡物")
        self.remove_pointer_radio.setChecked(True)

        self.type_group.addButton(self.remove_pointer_radio, 0)
        self.type_group.addButton(self.remove_obstruction_radio, 1)

        type_layout.addWidget(self.remove_pointer_radio)
        type_layout.addWidget(self.remove_obstruction_radio)
        layout.addLayout(type_layout)

        # 后处理选项
        bg_layout = QHBoxLayout()
        bg_layout.addWidget(QLabel("后处理选项:"))
        self.enable_background_removal = QCheckBox("去背景")
        self.enable_background_removal.setChecked(False)
        bg_layout.addWidget(self.enable_background_removal)
        layout.addLayout(bg_layout)

        # 连接信号
        self.remove_pointer_radio.toggled.connect(self.save_user_preferences)
        self.remove_obstruction_radio.toggled.connect(self.save_user_preferences)
        self.enable_background_removal.toggled.connect(self.save_user_preferences)

        # 按钮
        button_layout = QHBoxLayout()
        
        save_btn = QPushButton("手动保存设置")
        save_btn.clicked.connect(self.save_user_preferences)
        button_layout.addWidget(save_btn)
        
        load_btn = QPushButton("手动加载设置")
        load_btn.clicked.connect(self.load_user_preferences)
        button_layout.addWidget(load_btn)
        
        reset_btn = QPushButton("重置为默认")
        reset_btn.clicked.connect(self.reset_to_default)
        button_layout.addWidget(reset_btn)

        layout.addLayout(button_layout)

        # 状态显示
        self.status_label = QLabel("状态: 就绪")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def save_user_preferences(self):
        """保存用户偏好设置"""
        try:
            preferences = {
                "process_type": "remove_pointer" if self.remove_pointer_radio.isChecked() else "remove_obstruction",
                "enable_background_removal": self.enable_background_removal.isChecked()
            }
            
            preferences_file = os.path.join(os.path.dirname(__file__), "user_preferences.json")
            with open(preferences_file, 'w', encoding='utf-8') as f:
                json.dump(preferences, f, ensure_ascii=False, indent=2)
            
            self.status_label.setText(f"状态: 设置已保存 - {preferences}")
            print(f"用户偏好设置已保存: {preferences}")
        except Exception as e:
            self.status_label.setText(f"状态: 保存失败 - {str(e)}")
            print(f"保存用户偏好设置失败: {str(e)}")

    def load_user_preferences(self):
        """加载用户偏好设置"""
        try:
            preferences_file = os.path.join(os.path.dirname(__file__), "user_preferences.json")
            if not os.path.exists(preferences_file):
                self.status_label.setText("状态: 偏好设置文件不存在，使用默认设置")
                print("用户偏好设置文件不存在，使用默认设置")
                return
            
            with open(preferences_file, 'r', encoding='utf-8') as f:
                preferences = json.load(f)
            
            # 应用处理类型设置
            process_type = preferences.get("process_type", "remove_pointer")
            if process_type == "remove_pointer":
                self.remove_pointer_radio.setChecked(True)
            else:
                self.remove_obstruction_radio.setChecked(True)
            
            # 应用去背景设置
            enable_bg_removal = preferences.get("enable_background_removal", False)
            self.enable_background_removal.setChecked(enable_bg_removal)
            
            self.status_label.setText(f"状态: 设置已加载 - {preferences}")
            print(f"用户偏好设置已加载: {preferences}")
        except Exception as e:
            self.status_label.setText(f"状态: 加载失败 - {str(e)}")
            print(f"加载用户偏好设置失败: {str(e)}")

    def reset_to_default(self):
        """重置为默认设置"""
        self.remove_pointer_radio.setChecked(True)
        self.enable_background_removal.setChecked(False)
        self.status_label.setText("状态: 已重置为默认设置")

def main():
    app = QApplication(sys.argv)
    widget = TestMemoryWidget()
    widget.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
