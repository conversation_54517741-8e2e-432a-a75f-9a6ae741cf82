# 去背景和去遮挡物记忆功能说明

## 功能概述

现在，去指针工具已经具备了记忆功能，能够自动保存和恢复用户的选择偏好，包括：

1. **处理类型选择**：去指针 或 去遮挡物
2. **后处理选项**：是否启用去背景功能

## 功能特点

### 自动保存
- 当用户切换"去指针"和"去遮挡物"选项时，选择会自动保存
- 当用户勾选或取消"去背景"选项时，选择会自动保存
- 设置保存在程序目录下的 `user_preferences.json` 文件中

### 自动加载
- 程序启动时会自动加载上次保存的用户偏好设置
- 如果是第一次使用，会使用默认设置（去指针 + 不启用去背景）

### 持久化存储
- 用户的选择会永久保存，即使重启程序也会保持上次的设置
- 设置文件采用JSON格式，便于查看和修改

## 实现细节

### 保存的设置内容
```json
{
  "process_type": "remove_pointer",        // 或 "remove_obstruction"
  "enable_background_removal": false      // true 或 false
}
```

### 触发保存的操作
- 点击"去指针"单选按钮
- 点击"去遮挡物"单选按钮  
- 勾选或取消"去背景"复选框

### 加载时机
- 程序启动时自动加载
- 如果偏好设置文件不存在，使用默认设置

## 用户体验改进

1. **无感知操作**：用户不需要手动保存设置，所有选择都会自动记忆
2. **即时生效**：设置更改后立即保存，无需等待
3. **跨会话保持**：关闭程序重新打开后，设置依然保持
4. **容错处理**：如果设置文件损坏或不存在，会自动使用默认设置

## 技术实现

### 核心方法

1. **save_user_preferences()**
   - 获取当前UI控件的状态
   - 将设置保存到JSON文件
   - 包含错误处理和日志记录

2. **load_user_preferences()**
   - 从JSON文件读取设置
   - 应用设置到UI控件
   - 处理文件不存在的情况

3. **信号连接**
   - 将UI控件的状态变化信号连接到保存方法
   - 确保任何选择变化都会触发自动保存

### 文件位置
- 设置文件：`user_preferences.json`（与主程序同目录）
- 自动创建，无需手动配置

## 测试验证

可以通过以下步骤验证记忆功能：

1. 运行程序，选择"去遮挡物"和"去背景"
2. 关闭程序
3. 重新运行程序
4. 确认上次的选择被正确恢复

或者运行测试脚本：
```bash
python test_memory.py
```

## 注意事项

- 设置文件会在程序目录下自动创建
- 如果需要重置设置，可以删除 `user_preferences.json` 文件
- 设置文件采用UTF-8编码，支持中文内容
- 所有设置更改都会在日志中记录，便于调试
